import { useState, useCallback } from 'react'
import { useAuth } from '@/lib/providers/auth-provider'
import { createClient } from '@/lib/supabase/client'

interface SetupState {
  currentStep: number
  isLoading: boolean
  errors: Record<string, string>
  apiConfig: {
    supabaseUrl: string
    supabaseAnonKey: string
    geminiApiKey?: string
    mistralApiKey?: string
  }
  profileData: {
    firstName: string
    lastName: string
    email: string
    currency: string
    preferences: {
      aiResponseDetail: 'minimal' | 'detailed' | 'comprehensive'
      aiPersonality: 'professional' | 'friendly' | 'casual'
      aiLearning: boolean
    }
  }
}

export function useSetupFlow() {
  const { user } = useAuth()
  const supabase = createClient()
  
  const [setupState, setSetupState] = useState<SetupState>({
    currentStep: 1,
    isLoading: false,
    errors: {},
    apiConfig: {
      supabaseUrl: '',
      supabaseAnonKey: '',
    },
    profileData: {
      firstName: '',
      lastName: '',
      email: '',
      currency: 'USD',
      preferences: {
        aiResponseDetail: 'detailed',
        aiPersonality: 'professional',
        aiLearning: true,
      },
    },
  })

  const updateApiConfig = useCallback((config: Partial<SetupState['apiConfig']>) => {
    setSetupState(prev => ({
      ...prev,
      apiConfig: { ...prev.apiConfig, ...config },
    }))
  }, [])

  const updateProfileData = useCallback((data: Partial<SetupState['profileData']>) => {
    setSetupState(prev => ({
      ...prev,
      profileData: { ...prev.profileData, ...data },
    }))
  }, [])

  const setError = useCallback((field: string, message: string) => {
    setSetupState(prev => ({
      ...prev,
      errors: { ...prev.errors, [field]: message },
    }))
  }, [])

  const clearError = useCallback((field: string) => {
    setSetupState(prev => ({
      ...prev,
      errors: { ...prev.errors, [field]: '' },
    }))
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    setSetupState(prev => ({ ...prev, isLoading: loading }))
  }, [])

  const nextStep = useCallback(() => {
    setSetupState(prev => ({ 
      ...prev, 
      currentStep: Math.min(prev.currentStep + 1, 3) 
    }))
  }, [])

  const prevStep = useCallback(() => {
    setSetupState(prev => ({ 
      ...prev, 
      currentStep: Math.max(prev.currentStep - 1, 1) 
    }))
  }, [])

  const testSupabaseConnection = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/setup/test-supabase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: setupState.apiConfig.supabaseUrl,
          anonKey: setupState.apiConfig.supabaseAnonKey,
        }),
      })

      const result = await response.json()
      if (!result.success) {
        setError('supabase', result.error || 'Connection failed')
        return false
      }

      clearError('supabase')
      return true
    } catch (error) {
      setError('supabase', 'Network error')
      return false
    } finally {
      setLoading(false)
    }
  }, [setupState.apiConfig, setLoading, setError, clearError])

  const testGeminiConnection = useCallback(async () => {
    if (!setupState.apiConfig.geminiApiKey) return true

    setLoading(true)
    try {
      const response = await fetch('/api/setup/test-gemini', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          apiKey: setupState.apiConfig.geminiApiKey,
        }),
      })

      const result = await response.json()
      if (!result.success) {
        setError('gemini', result.error || 'Invalid API key')
        return false
      }

      clearError('gemini')
      return true
    } catch (error) {
      setError('gemini', 'Network error')
      return false
    } finally {
      setLoading(false)
    }
  }, [setupState.apiConfig.geminiApiKey, setLoading, setError, clearError])

  const completeSetup = useCallback(async () => {
    setLoading(true)
    try {
      // Save API configuration
      const { error: configError } = await supabase
        .from('api_configurations')
        .insert({
          user_id: user?.id || '',
          supabase_url: setupState.apiConfig.supabaseUrl,
          supabase_anon_key: setupState.apiConfig.supabaseAnonKey,
          gemini_api_key: setupState.apiConfig.geminiApiKey || null,
          mistral_api_key: setupState.apiConfig.mistralApiKey || null,
          is_configured: true,
        })

      if (configError) throw configError

      // Update user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: user?.id || '',
          first_name: setupState.profileData.firstName,
          last_name: setupState.profileData.lastName,
          email: setupState.profileData.email,
          currency: setupState.profileData.currency,
          updated_at: new Date().toISOString(),
        })

      if (profileError) throw profileError

      return true
    } catch (error) {
      setError('general', error instanceof Error ? error.message : 'Setup failed')
      return false
    } finally {
      setLoading(false)
    }
  }, [user, setupState, supabase, setLoading, setError])

  return {
    ...setupState,
    updateApiConfig,
    updateProfileData,
    setError,
    clearError,
    setLoading,
    nextStep,
    prevStep,
    testSupabaseConnection,
    testGeminiConnection,
    completeSetup,
  }
}
