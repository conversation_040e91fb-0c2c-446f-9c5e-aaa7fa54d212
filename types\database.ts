// Database types generated from Supabase
// This file will be auto-generated by: npm run supabase:gen-types

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          first_name: string | null
          last_name: string | null
          email: string
          avatar_url: string | null
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          first_name?: string | null
          last_name?: string | null
          email: string
          avatar_url?: string | null
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string | null
          last_name?: string | null
          email?: string
          avatar_url?: string | null
          currency?: string
          created_at?: string
          updated_at?: string
        }
      }
      accounts: {
        Row: {
          id: string
          user_id: string
          name: string
          type: 'checking' | 'savings' | 'credit' | 'investment'
          balance: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type: 'checking' | 'savings' | 'credit' | 'investment'
          balance?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: 'checking' | 'savings' | 'credit' | 'investment'
          balance?: number
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          user_id: string
          name: string
          type: 'income' | 'expense'
          color: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type: 'income' | 'expense'
          color?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: 'income' | 'expense'
          color?: string
          created_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          user_id: string
          account_id: string
          category_id: string | null
          amount: number
          description: string
          date: string
          type: 'income' | 'expense' | 'transfer'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          account_id: string
          category_id?: string | null
          amount: number
          description: string
          date: string
          type: 'income' | 'expense' | 'transfer'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          account_id?: string
          category_id?: string | null
          amount?: number
          description?: string
          date?: string
          type?: 'income' | 'expense' | 'transfer'
          created_at?: string
          updated_at?: string
        }
      }
      recurring_payments: {
        Row: {
          id: string
          user_id: string
          name: string
          amount: number
          frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
          next_due_date: string
          category_id: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          amount: number
          frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
          next_due_date: string
          category_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          amount?: number
          frequency?: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
          next_due_date?: string
          category_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      loans_debits: {
        Row: {
          id: string
          user_id: string
          name: string
          total_amount: number
          remaining_amount: number
          interest_rate: number
          monthly_payment: number
          due_date: string
          type: 'loan' | 'debt'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          total_amount: number
          remaining_amount: number
          interest_rate: number
          monthly_payment: number
          due_date: string
          type: 'loan' | 'debt'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          total_amount?: number
          remaining_amount?: number
          interest_rate?: number
          monthly_payment?: number
          due_date?: string
          type?: 'loan' | 'debt'
          created_at?: string
          updated_at?: string
        }
      }
      ai_analysis_results: {
        Row: {
          id: string
          user_id: string
          analysis_type: string
          insights: any // JSON object
          recommendations: string[]
          risk_score: number
          confidence_level: number
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          analysis_type: string
          insights: any
          recommendations: string[]
          risk_score: number
          confidence_level: number
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          analysis_type?: string
          insights?: any
          recommendations?: string[]
          risk_score?: number
          confidence_level?: number
          created_at?: string
        }
      }
      api_configurations: {
        Row: {
          id: string
          user_id: string
          supabase_url: string
          supabase_anon_key: string
          gemini_api_key: string | null
          mistral_api_key: string | null
          is_configured: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          supabase_url: string
          supabase_anon_key: string
          gemini_api_key?: string | null
          mistral_api_key?: string | null
          is_configured?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          supabase_url?: string
          supabase_anon_key?: string
          gemini_api_key?: string | null
          mistral_api_key?: string | null
          is_configured?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      account_type: 'checking' | 'savings' | 'credit' | 'investment'
      transaction_type: 'income' | 'expense' | 'transfer'
      category_type: 'income' | 'expense'
      payment_frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
      loan_type: 'loan' | 'debt'
    }
  }
}
