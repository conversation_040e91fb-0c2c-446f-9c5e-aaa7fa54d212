# Documentation Routing Map - AI-Powered Personal Finance Tracker

## Overview
This documentation system follows the Claude Code Development Kit's 3-tier architecture for optimal AI assistance and context management.

## Tier 1 - Foundation Documentation (Auto-loaded)
**Location**: `/docs/ai-context/`
**Purpose**: Core project context that loads automatically with every Claude Code session

### Core Files
- **`project-structure.md`** - Complete technology stack and file tree
- **`docs-overview.md`** - This file - documentation routing guide  
- **`system-integration.md`** - Cross-component integration patterns
- **`deployment-infrastructure.md`** - Infrastructure and deployment context
- **`handoff.md`** - Session continuity and context preservation

### When These Load
- Every Claude Code command execution
- Project initialization
- Context refresh operations
- Cross-session handoffs

## Tier 2 - Component Documentation
**Location**: Component directories with `CLAUDE.md` files
**Purpose**: Component-specific context for focused development

### Component Context Files
- **`/components/CLAUDE.md`** - React component standards and TailGrids integration
- **`/lib/CLAUDE.md`** - Utility functions and service integrations
- **`/hooks/CLAUDE.md`** - Custom React hooks documentation
- **`/types/CLAUDE.md`** - TypeScript definitions and schemas

### When These Load
- When working within specific component directories
- Component creation and modification tasks
- Refactoring operations
- Testing component-specific functionality

## Tier 3 - Feature Documentation  
**Location**: Feature directories with `CLAUDE.md` files
**Purpose**: Feature-specific implementation details and patterns

### Feature Context Files
- **`/app/setup/CLAUDE.md`** - Onboarding flow implementation
- **`/app/ai-chat/CLAUDE.md`** - AI chat interface patterns
- **`/app/transactions/CLAUDE.md`** - Transaction management logic
- **`/app/loans-debits/CLAUDE.md`** - Loans and debits tracking
- **`/app/recurring-payments/CLAUDE.md`** - Subscription management
- **`/app/ai-analysis/CLAUDE.md`** - AI analysis and insights
- **`/app/settings/CLAUDE.md`** - Configuration and settings

### When These Load
- Feature-specific development tasks
- Bug fixes within features
- Feature testing and validation
- Feature-specific documentation updates

## Documentation Routing Rules

### Command-Based Loading
```markdown
/full-context → Loads Tier 1 + All Tier 2 + Relevant Tier 3
/code-review → Loads Tier 1 + Modified files' contexts
/update-docs → Loads Tier 1 + Documentation files
/test-suite → Loads Tier 1 + Test-related contexts
/deploy-check → Loads Tier 1 + Infrastructure contexts
```

### Directory-Based Loading
```markdown
Working in /app/setup/ → Tier 1 + /app/setup/CLAUDE.md
Working in /components/ → Tier 1 + /components/CLAUDE.md  
Working in /lib/ai/ → Tier 1 + /lib/CLAUDE.md
Working in /app/transactions/ → Tier 1 + /app/transactions/CLAUDE.md
```

### Task Complexity Loading
```markdown
Simple tasks → Tier 1 only
Medium tasks → Tier 1 + Relevant Tier 2
Complex tasks → Tier 1 + Tier 2 + Relevant Tier 3
```

## Special Documentation Categories

### Issue Tracking
**Location**: `/docs/open-issues/`
**Purpose**: Track and manage development issues

- **`setup-issues.md`** - Configuration and setup problems
- **`ui-issues.md`** - User interface and experience issues  
- **`integration-issues.md`** - AI and API integration problems
- **`performance-issues.md`** - Performance and optimization issues

### Feature Specifications
**Location**: `/docs/specs/`
**Purpose**: Detailed feature requirements and specifications

- **`setup-flow-spec.md`** - Complete onboarding flow specification
- **`dashboard-spec.md`** - Dashboard layout and functionality requirements
- **`ai-chat-spec.md`** - AI chat interface and conversation patterns
- **`transaction-spec.md`** - Transaction management workflows
- **`analysis-spec.md`** - AI analysis features and algorithms

### TailGrids Integration
**Location**: Throughout project with special markers
**Purpose**: Guide proper usage of existing TailGrids components

#### Component Mapping
```markdown
Setup Forms → core-components/inputs/, core-components/buttons/
Dashboard Cards → application/cards/
Navigation → application/navbars/
Tables → application/tables/, application/table-grids/
Modals → core-components/modals/, application/modals/
Alerts → core-components/alerts/
Progress → core-components/progress-bars/
```

## Context Optimization Strategies

### Automatic Context Management
- **Smart Loading**: Only relevant documentation loads based on task
- **Context Compression**: Summarize non-critical information
- **Session Continuity**: Preserve essential context across sessions
- **Token Efficiency**: Minimize redundant information loading

### Manual Context Control
```markdown
@/CLAUDE.md → Load master context
@/docs/ai-context/project-structure.md → Load complete file tree
@/docs/ai-context/docs-overview.md → Load this routing guide
@/app/[feature]/CLAUDE.md → Load specific feature context
```

### Context Refresh Triggers
- File modifications in watched directories
- New feature branch creation  
- Deployment preparation
- Cross-team handoffs
- Major refactoring operations

## Documentation Maintenance

### Automated Updates
- **Command**: `/update-docs` - Maintains documentation currency
- **Triggers**: After significant code changes
- **Scope**: Updates affected Tier 2 and Tier 3 files
- **Validation**: Ensures documentation matches implementation

### Manual Maintenance
- **Weekly Reviews**: Tier 1 documentation accuracy
- **Feature Completion**: Update Tier 3 feature docs
- **Quarterly Audits**: Complete documentation system review
- **Team Handoffs**: Update handoff documentation

## Integration with Claude Code Commands

### Context-Aware Commands
All custom commands automatically load appropriate documentation tiers:

```markdown
/setup-api → Tier 1 + Setup contexts + API integration docs
/create-component → Tier 1 + Component contexts + TailGrids docs  
/ai-analysis → Tier 1 + AI contexts + Analysis feature docs
/deploy-check → Tier 1 + Infrastructure contexts + Deployment docs
```

### Documentation Commands
```markdown
/docs-overview → Show this routing map
/docs-health → Check documentation completeness
/docs-update → Update all documentation files
/docs-export → Export documentation for external use
```

This routing system ensures that Claude Code always has the right context for any development task while maintaining optimal performance and token efficiency.
