<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Toast | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../../assets/css/tailwind.css" />
  </head>
  <body>
    <!-- ====== Toast Section Start -->
    <section class="bg-gray-2 py-[60px] dark:bg-dark">
      <div class="mx-auto px-4 sm:container">
        <div
          class="relative flex max-w-[560px] rounded-lg border border-stroke bg-white p-[25px] shadow-testimonial-6 dark:border-dark-3 dark:bg-dark-2 dark:shadow-box-dark"
        >
          <div class="mr-6 flex w-full max-w-[34px] justify-center">
            <span>
              <svg
                width="34"
                height="34"
                viewBox="0 0 34 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.6438 33.0969C12.0062 33.0969 11.4219 32.8313 10.9969 32.3531C10.2 31.45 10.4656 30.3875 10.5719 29.9094L13.3875 17.85H7.91563C7.54375 17.85 6.8 17.85 6.1625 17.3719C5.1 16.5219 5.41875 15.0875 5.47188 14.6094L7.70313 3.71875C7.80938 3.13437 8.02188 2.23125 8.81875 1.59375C9.72188 0.903125 10.625 0.903125 11.1563 0.95625H18.4344C19.0719 0.95625 20.4 0.95625 21.1969 1.96562C21.9938 2.975 21.7281 4.30312 21.6219 4.94062L20.9844 7.96875L25.6594 8.02187C27.5187 8.02187 28.2094 8.87188 28.4219 9.5625C28.7937 10.625 28.1562 11.5813 27.8906 11.9531L14.875 31.5563C14.6094 31.9281 14.2375 32.5125 13.5469 32.8844C13.2813 32.9906 13.0156 33.0969 12.75 33.0969C12.75 33.0438 12.6969 33.0969 12.6438 33.0969ZM7.8625 15.4062C7.91563 15.4062 7.96875 15.4062 7.96875 15.4062H14.9281C15.3 15.4062 15.6188 15.5656 15.8844 15.8844C16.0969 16.15 16.2031 16.5219 16.0969 16.8937L13.0156 30.0687L25.8719 10.625C25.925 10.5719 25.9781 10.4656 25.9781 10.4125C25.8719 10.4125 25.7656 10.4125 25.5531 10.4125L19.4437 10.3594C19.0719 10.3594 18.7531 10.2 18.5406 9.93438C18.3281 9.66875 18.2219 9.29687 18.3281 8.925L19.2844 4.40937C19.4438 3.66562 19.3906 3.50625 19.3375 3.45312C19.2844 3.4 19.125 3.34687 18.3812 3.34687H11.1563C10.7313 3.34687 10.4656 3.34687 10.4125 3.4C10.3594 3.45312 10.2531 3.71875 10.1469 4.14375L7.91563 15.0344C7.91563 15.1937 7.8625 15.3 7.8625 15.4062Z"
                  fill="#3758F9"
                />
              </svg>
            </span>
          </div>
          <div class="w-full pr-4 sm:pr-10">
            <h6
              class="mb-2 text-base font-semibold text-dark sm:text-lg dark:text-white"
            >
              New update! available
            </h6>
            <p class="mb-5 text-sm text-body-color dark:text-dark-6">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
              nec ligula at dolor aliquam mollis.
            </p>
            <button class="text-base font-medium text-primary hover:underline">
              Update now
            </button>
          </div>
          <button
            class="hover:text-danger absolute right-4 top-4 text-dark-5 dark:text-dark-6"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="fill-current"
            >
              <g clip-path="url(#clip0_1088_26057)">
                <path
                  d="M8.79999 7.99999L14.9 1.89999C15.125 1.67499 15.125 1.32499 14.9 1.09999C14.675 0.874994 14.325 0.874994 14.1 1.09999L7.99999 7.19999L1.89999 1.09999C1.67499 0.874994 1.32499 0.874994 1.09999 1.09999C0.874994 1.32499 0.874994 1.67499 1.09999 1.89999L7.19999 7.99999L1.09999 14.1C0.874994 14.325 0.874994 14.675 1.09999 14.9C1.19999 15 1.34999 15.075 1.49999 15.075C1.64999 15.075 1.79999 15.025 1.89999 14.9L7.99999 8.79999L14.1 14.9C14.2 15 14.35 15.075 14.5 15.075C14.65 15.075 14.8 15.025 14.9 14.9C15.125 14.675 15.125 14.325 14.9 14.1L8.79999 7.99999Z"
                />
              </g>
              <defs>
                <clipPath id="clip0_1088_26057">
                  <rect width="16" height="16" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>
      </div>
    </section>
    <!-- ====== Toast Section End -->
  </body>
</html>
