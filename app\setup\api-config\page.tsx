'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'

export default function ApiConfigPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    supabaseUrl: '',
    supabaseAnonKey: '',
    geminiApiKey: '',
    mistralApiKey: ''
  })
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const testSupabaseConnection = async () => {
    setIsLoading(true)
    try {
      // Test Supabase connection
      const response = await fetch('/api/setup/test-supabase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: formData.supabaseUrl,
          anonKey: formData.supabaseAnonKey
        })
      })
      
      const result = await response.json()
      setTestResults(prev => ({ ...prev, supabase: result.success }))
      
      if (!result.success) {
        setErrors(prev => ({ ...prev, supabase: result.error || 'Connection failed' }))
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, supabase: false }))
      setErrors(prev => ({ ...prev, supabase: 'Network error' }))
    } finally {
      setIsLoading(false)
    }
  }

  const testGeminiConnection = async () => {
    if (!formData.geminiApiKey) return
    
    setIsLoading(true)
    try {
      const response = await fetch('/api/setup/test-gemini', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey: formData.geminiApiKey })
      })
      
      const result = await response.json()
      setTestResults(prev => ({ ...prev, gemini: result.success }))
      
      if (!result.success) {
        setErrors(prev => ({ ...prev, gemini: result.error || 'API key invalid' }))
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, gemini: false }))
      setErrors(prev => ({ ...prev, gemini: 'Network error' }))
    } finally {
      setIsLoading(false)
    }
  }

  const testMistralConnection = async () => {
    if (!formData.mistralApiKey) return
    
    setIsLoading(true)
    try {
      const response = await fetch('/api/setup/test-mistral', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey: formData.mistralApiKey })
      })
      
      const result = await response.json()
      setTestResults(prev => ({ ...prev, mistral: result.success }))
      
      if (!result.success) {
        setErrors(prev => ({ ...prev, mistral: result.error || 'API key invalid' }))
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, mistral: false }))
      setErrors(prev => ({ ...prev, mistral: 'Network error' }))
    } finally {
      setIsLoading(false)
    }
  }

  const handleNext = () => {
    // Validate required fields
    const newErrors: Record<string, string> = {}
    
    if (!formData.supabaseUrl) newErrors.supabaseUrl = 'Supabase URL is required'
    if (!formData.supabaseAnonKey) newErrors.supabaseAnonKey = 'Supabase Anon Key is required'
    if (!testResults.supabase) newErrors.supabase = 'Supabase connection must be tested successfully'

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    // Save configuration and proceed
    localStorage.setItem('setupApiConfig', JSON.stringify(formData))
    router.push('/setup/profile-setup')
  }

  const allTestsPassed = testResults.supabase && 
    (formData.geminiApiKey ? testResults.gemini !== false : true) &&
    (formData.mistralApiKey ? testResults.mistral !== false : true)

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={33} className="w-full" />
          <p className="text-sm text-gray-600 mt-2">Step 1 of 3: API Configuration</p>
        </div>

        <Card className="p-8">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Configure API Services</h1>
            <p className="text-gray-600 mt-2">
              Set up your database and AI services to get started with intelligent finance tracking.
            </p>
          </div>

          <div className="space-y-8">
            {/* Supabase Configuration */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">Supabase Database Setup</h2>
              
              <div>
                <label htmlFor="supabaseUrl" className="block text-sm font-medium text-gray-700 mb-2">
                  Supabase URL *
                </label>
                <Input
                  id="supabaseUrl"
                  type="url"
                  placeholder="https://your-project.supabase.co"
                  value={formData.supabaseUrl}
                  onChange={(e) => handleInputChange('supabaseUrl', e.target.value)}
                  className={errors.supabaseUrl ? 'border-red-500' : ''}
                />
                {errors.supabaseUrl && (
                  <p className="text-red-500 text-sm mt-1">{errors.supabaseUrl}</p>
                )}
              </div>

              <div>
                <label htmlFor="supabaseAnonKey" className="block text-sm font-medium text-gray-700 mb-2">
                  Supabase Anon Key *
                </label>
                <Input
                  id="supabaseAnonKey"
                  type="password"
                  placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  value={formData.supabaseAnonKey}
                  onChange={(e) => handleInputChange('supabaseAnonKey', e.target.value)}
                  className={errors.supabaseAnonKey ? 'border-red-500' : ''}
                />
                {errors.supabaseAnonKey && (
                  <p className="text-red-500 text-sm mt-1">{errors.supabaseAnonKey}</p>
                )}
              </div>

              <Button 
                onClick={testSupabaseConnection}
                disabled={!formData.supabaseUrl || !formData.supabaseAnonKey || isLoading}
                variant={testResults.supabase ? 'success' : 'secondary'}
              >
                {isLoading ? 'Testing...' : testResults.supabase ? 'Connection Verified ✓' : 'Test Connection'}
              </Button>

              {errors.supabase && (
                <Alert variant="error">
                  {errors.supabase}
                </Alert>
              )}
            </div>

            {/* Google Gemini Configuration */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">Google Gemini AI (Optional)</h2>
              
              <div>
                <label htmlFor="geminiApiKey" className="block text-sm font-medium text-gray-700 mb-2">
                  Gemini API Key
                </label>
                <Input
                  id="geminiApiKey"
                  type="password"
                  placeholder="AIzaSyC..."
                  value={formData.geminiApiKey}
                  onChange={(e) => handleInputChange('geminiApiKey', e.target.value)}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Used for advanced financial analysis and insights
                </p>
              </div>

              {formData.geminiApiKey && (
                <Button 
                  onClick={testGeminiConnection}
                  disabled={isLoading}
                  variant={testResults.gemini ? 'success' : 'secondary'}
                >
                  {isLoading ? 'Testing...' : testResults.gemini ? 'API Key Valid ✓' : 'Validate Key'}
                </Button>
              )}

              {errors.gemini && (
                <Alert variant="error">
                  {errors.gemini}
                </Alert>
              )}
            </div>

            {/* MistralAI Configuration */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">MistralAI (Optional)</h2>
              
              <div>
                <label htmlFor="mistralApiKey" className="block text-sm font-medium text-gray-700 mb-2">
                  MistralAI API Key
                </label>
                <Input
                  id="mistralApiKey"
                  type="password"
                  placeholder="sk-..."
                  value={formData.mistralApiKey}
                  onChange={(e) => handleInputChange('mistralApiKey', e.target.value)}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Used for transaction categorization and natural language processing
                </p>
              </div>

              {formData.mistralApiKey && (
                <Button 
                  onClick={testMistralConnection}
                  disabled={isLoading}
                  variant={testResults.mistral ? 'success' : 'secondary'}
                >
                  {isLoading ? 'Testing...' : testResults.mistral ? 'API Key Valid ✓' : 'Verify Access'}
                </Button>
              )}

              {errors.mistral && (
                <Alert variant="error">
                  {errors.mistral}
                </Alert>
              )}
            </div>
          </div>

          <div className="flex justify-end mt-8">
            <Button 
              onClick={handleNext}
              disabled={!allTestsPassed}
              className="px-8"
            >
              Next: Profile Setup →
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}
