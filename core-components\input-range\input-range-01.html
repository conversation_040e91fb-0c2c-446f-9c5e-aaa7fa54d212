<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Input Range Slider | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />

    <link rel="stylesheet" href="../../assets/css/tailwind.css" />
    <script defer src="../../assets/js/alpine.min.js"></script>
  </head>
  <body>
    <!-- ====== Input Range Slider Start -->
    <section class="bg-white py-20 dark:bg-dark">
      <div class="container">
        <div class="mx-auto w-full max-w-[570px]">
          <div
            x-data="range()"
            x-init="minTrigger(); maxTrigger()"
            class="relative w-full max-w-xl"
          >
            <div>
              <input
                type="range"
                step="100"
                x-bind:min="min"
                x-bind:max="max"
                x-on:input="minTrigger"
                x-model="minValue"
                class="pointer-events-none absolute z-20 h-2 w-full cursor-pointer appearance-none opacity-0"
              />

              <input
                type="range"
                step="100"
                x-bind:min="min"
                x-bind:max="max"
                x-on:input="maxTrigger"
                x-model="maxValue"
                class="pointer-events-none absolute z-20 h-2 w-full cursor-pointer appearance-none opacity-0"
              />

              <div class="relative z-10 h-2">
                <div
                  class="absolute bottom-0 left-0 right-0 top-0 z-10 rounded-md bg-gray-3 dark:bg-dark-2"
                ></div>

                <div
                  class="absolute bottom-0 top-0 z-20 rounded-md bg-primary"
                  x-bind:style="'right:'+maxThumb+'%; left:'+minThumb+'%'"
                ></div>

                <div
                  class="absolute left-0 top-0 z-30 -ml-1 -mt-2 h-6 w-6 rounded-full border-2 border-primary bg-white"
                  x-bind:style="'left: '+minThumb+'%'"
                ></div>

                <div
                  class="absolute right-0 top-0 z-30 -mr-3 -mt-2 h-6 w-6 rounded-full border-2 border-primary bg-white"
                  x-bind:style="'right: '+maxThumb+'%'"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- ====== Input Range Slider End -->
    <script>
      function range() {
        return {
          minValue: 1000,
          maxValue: 7000,
          min: 100,
          max: 10000,
          minThumb: 0,
          maxThumb: 0,

          minTrigger() {
            this.minValue = Math.min(this.minValue, this.maxValue - 500);
            this.minThumb =
              ((this.minValue - this.min) / (this.max - this.min)) * 100;
          },

          maxTrigger() {
            this.maxValue = Math.max(this.maxValue, this.minValue + 500);
            this.maxThumb =
              100 - ((this.maxValue - this.min) / (this.max - this.min)) * 100;
          },
        };
      }
    </script>
  </body>
</html>
