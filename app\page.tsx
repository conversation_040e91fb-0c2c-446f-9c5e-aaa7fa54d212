import { Suspense } from 'react'
import { DashboardSkeleton } from '@/components/ui/loading-skeletons'
import { FinancialSummaryCards } from '@/components/dashboard/financial-summary-cards'
import { RecentTransactionsTable } from '@/components/dashboard/recent-transactions-table'
import { SpendingChart } from '@/components/dashboard/spending-chart'
import { UpcomingBills } from '@/components/dashboard/upcoming-bills'

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Dashboard Content */}
      <div className="container mx-auto p-6">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Financial Dashboard</h1>
          <p className="text-gray-600 mt-2">Get insights into your financial health with AI-powered analysis</p>
        </div>

        {/* Financial Summary Cards */}
        <Suspense fallback={<DashboardSkeleton />}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <FinancialSummaryCards />
          </div>
        </Suspense>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Suspense fallback={<div className="h-64 bg-white rounded-lg animate-pulse" />}>
            <SpendingChart />
          </Suspense>
          
          <Suspense fallback={<div className="h-64 bg-white rounded-lg animate-pulse" />}>
            <UpcomingBills />
          </Suspense>
        </div>

        {/* Recent Transactions */}
        <Suspense fallback={<div className="h-96 bg-white rounded-lg animate-pulse" />}>
          <RecentTransactionsTable />
        </Suspense>
      </div>
    </div>
  )
}
