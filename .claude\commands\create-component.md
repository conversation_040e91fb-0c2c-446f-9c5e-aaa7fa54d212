# Create TailGrids-Integrated Component

Create a new React component: $ARGUMENTS

Please follow these steps to create a new component that properly integrates with the TailGrids library:

## Component Analysis
- Analyze the component requirements from the provided description
- Identify which existing TailGrids components can be leveraged
- Determine if this is a simple wrapper or complex composite component
- Check for similar existing components in the TailGrids library

## TailGrids Component Selection
- Browse the available TailGrids components in:
  - `core-components/` for basic UI elements
  - `application/` for complex patterns
  - `marketing/` for promotional elements
- Identify the most appropriate base components to extend
- Note any styling patterns or utilities to reuse

## Component Structure Planning
- Plan the component API (props, callbacks, variants)
- Define TypeScript interfaces for all props
- Design the component composition using TailGrids elements
- Plan for accessibility and responsive design

## Implementation Guidelines
- Create the component in the appropriate directory
- Use proper TypeScript typing with strict mode
- Implement proper error boundaries and loading states
- Follow established naming conventions

## TailGrids Integration Best Practices
- Import TailGrids components using the established patterns
- Extend TailGrids components rather than recreating them
- Maintain consistent styling with the TailGrids design system
- Use TailGrids utility classes and custom properties

## Component Structure Template
```typescript
import React from 'react'
import { cn } from '@/lib/utils'
// Import relevant TailGrids components

interface ComponentNameProps extends React.HTMLAttributes<HTMLElement> {
  // Define component-specific props
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  // Add other props as needed
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  ...props
}) => {
  return (
    // Implement component using TailGrids elements
  )
}

ComponentName.displayName = 'ComponentName'
```

## Testing Requirements
- Create unit tests for the new component
- Test all prop variations and edge cases
- Verify TailGrids integration works correctly
- Test accessibility features and keyboard navigation

## Documentation Creation
- Add component documentation to appropriate CLAUDE.md file
- Document props, usage examples, and best practices
- Include accessibility considerations
- Document any TailGrids dependencies

## Integration Verification
- Ensure component works with existing form libraries
- Verify proper TypeScript integration
- Test component in different contexts (modal, sidebar, etc.)
- Validate responsive behavior across screen sizes

## Example Usage Documentation
- Provide clear usage examples
- Show common patterns and variations
- Document integration with forms or data
- Include troubleshooting common issues

## Component Export
- Add component to appropriate index.ts file
- Ensure proper tree-shaking support
- Update component library exports
- Add to component documentation

## Quality Checklist
- [ ] Component follows TailGrids design patterns
- [ ] TypeScript types are properly defined
- [ ] Accessibility requirements are met
- [ ] Responsive design is implemented
- [ ] Component is properly tested
- [ ] Documentation is complete
- [ ] Integration with existing components verified
- [ ] Performance impact assessed

Please create a fully functional, well-documented component that seamlessly integrates with the existing TailGrids library and project architecture.
