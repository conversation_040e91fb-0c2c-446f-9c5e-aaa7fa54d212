<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skeleton | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../../assets/css/tailwind.css" />
  </head>
  <body>
    <!-- ====== Skeleton Start -->
    <section class="bg-white py-20 dark:bg-dark">
      <div class="container">
        <div class="mx-auto w-full max-w-[700px] items-center gap-8 md:flex">
          <div
            class="flex h-[200px] w-full max-w-[300px] items-center justify-center rounded-xl bg-linear-to-r from-gray-1 to-gray-4 text-secondary-color dark:from-dark-4 dark:to-dark-5"
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.84995 31.15C4.49995 31.15 4.09995 31.05 3.79995 30.85C3.09995 30.45 2.69995 29.8 2.69995 29V2.99999C2.69995 2.24999 3.09995 1.54999 3.79995 1.14999C4.49995 0.749994 5.29995 0.799994 5.99995 1.19999L28.4 14.25C29.05 14.65 29.4 15.3 29.4 16.05C29.4 16.75 29.05 17.45 28.4 17.8L5.94995 30.8C5.59995 31 5.19995 31.15 4.84995 31.15ZM4.89995 3.19999V28.8L26.9 16L4.89995 3.19999Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div class="w-full space-y-3">
            <div
              class="h-3 w-full rounded-full bg-linear-to-r from-gray-1 to-gray-4 dark:from-dark-4 dark:to-dark-5"
            ></div>
            <div
              class="h-3 w-4/6 rounded-full bg-linear-to-r from-gray-1 to-gray-4 dark:from-dark-4 dark:to-dark-5"
            ></div>
            <div
              class="h-3 w-5/6 rounded-full bg-linear-to-r from-gray-1 to-gray-4 dark:from-dark-4 dark:to-dark-5"
            ></div>
            <div
              class="h-3 w-full rounded-full bg-linear-to-r from-gray-1 to-gray-4 dark:from-dark-4 dark:to-dark-5"
            ></div>
            <div
              class="h-3 w-5/6 rounded-full bg-linear-to-r from-gray-1 to-gray-4 dark:from-dark-4 dark:to-dark-5"
            ></div>
            <div
              class="h-3 w-4/6 rounded-full bg-linear-to-r from-gray-1 to-gray-4 dark:from-dark-4 dark:to-dark-5"
            ></div>
          </div>
        </div>
      </div>
    </section>
    <!-- ====== Skeleton End -->
  </body>
</html>
