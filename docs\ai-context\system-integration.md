# System Integration Patterns - AI-Powered Personal Finance Tracker

## Overview
This document defines the cross-component integration patterns for the AI-powered personal finance tracker, focusing on how TailGrids components, Next.js features, Supabase services, and AI integrations work together.

## Core Integration Architecture

### TailGrids + Next.js Integration
```typescript
// app/globals.css - TailGrids plugin integration
@tailwind base;
@tailwind components; 
@tailwind utilities;
@plugin 'tailgrids/plugin';

// Custom component extension pattern
import { Button } from '@/components/ui/button'
// Based on core-components/buttons/buttons.html

export interface ExtendedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  loadingText?: string
}

export const ExtendedButton: React.FC<ExtendedButtonProps> = ({
  children,
  loading,
  loadingText,
  ...props
}) => {
  return (
    <Button {...props} disabled={loading || props.disabled}>
      {loading ? (
        <>
          <SpinnerIcon className="mr-2" />
          {loadingText || 'Loading...'}
        </>
      ) : children}
    </Button>
  )
}
```

### Supabase + Next.js Integration
```typescript
// lib/supabase/client.ts - Client configuration
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from '@/types/database'

export const supabase = createClientComponentClient<Database>()

// lib/supabase/server.ts - Server configuration  
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export const createServerSupabaseClient = () =>
  createServerComponentClient<Database>({ cookies })

// middleware.ts - Authentication middleware
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  
  const { data: { session } } = await supabase.auth.getSession()
  
  // Redirect to setup if not configured
  if (!session && !req.nextUrl.pathname.startsWith('/setup')) {
    return NextResponse.redirect(new URL('/setup/api-config', req.url))
  }
  
  return res
}
```

### AI Services Integration Pattern
```typescript
// lib/ai/gemini.ts - Gemini API integration
import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY!)

export async function analyzeFinancialData(
  transactions: Transaction[],
  context: AnalysisContext
): Promise<FinancialInsight[]> {
  const model = genAI.getGenerativeModel({ model: 'gemini-pro' })
  
  const prompt = `
    Analyze the following financial transactions and provide insights:
    ${JSON.stringify(transactions)}
    
    Context: ${JSON.stringify(context)}
    
    Provide insights in the following format:
    - Spending patterns
    - Budget recommendations  
    - Saving opportunities
    - Risk assessments
  `
  
  try {
    const result = await model.generateContent(prompt)
    return parseInsights(result.response.text())
  } catch (error) {
    console.error('Gemini API error:', error)
    throw new Error('Failed to analyze financial data')
  }
}

// lib/ai/mistral.ts - MistralAI integration
export async function categorizeTransaction(
  description: string,
  amount: number
): Promise<TransactionCategory> {
  // Similar pattern for MistralAI
}
```

## Component Integration Patterns

### Form Integration with TailGrids
```typescript
// components/forms/setup-forms/ApiConfigForm.tsx
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert } from '@/components/ui/alert'
import { useSupabaseTest } from '@/hooks/use-setup'

export const ApiConfigForm: React.FC = () => {
  const { testConnection, isLoading, error } = useSupabaseTest()
  
  return (
    <form className="space-y-6">
      {/* Uses core-components/inputs/input.html styling */}
      <Input
        label="Supabase URL"
        placeholder="https://your-project.supabase.co"
        required
      />
      
      {/* Uses core-components/buttons/buttons.html styling */}  
      <Button
        type="submit"
        loading={isLoading}
        className="w-full"
      >
        Test Connection
      </Button>
      
      {/* Uses core-components/alerts/alerts.html styling */}
      {error && (
        <Alert variant="error">
          Connection failed: {error.message}
        </Alert>
      )}
    </form>
  )
}
```

### Dashboard Integration Pattern
```typescript
// app/page.tsx - Dashboard with TailGrids cards
import { Card } from '@/components/ui/card'
import { FinancialSummaryChart } from '@/components/charts/dashboard-charts'
import { RecentTransactionsTable } from '@/components/tables'

export default async function DashboardPage() {
  const { data: summary } = await getFinancialSummary()
  const { data: transactions } = await getRecentTransactions()
  
  return (
    <div className="container mx-auto p-6">
      {/* Financial summary cards using application/cards/card-01.html */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card title="Total Balance" value={summary.balance} />
        <Card title="Monthly Income" value={summary.income} />
        <Card title="Monthly Expenses" value={summary.expenses} />
      </div>
      
      {/* Charts section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <FinancialSummaryChart data={summary.chartData} />
      </div>
      
      {/* Transactions table using application/tables/tables-01.html */}
      <RecentTransactionsTable transactions={transactions} />
    </div>
  )
}
```

## Database Integration Patterns

### Transaction Management
```typescript
// lib/supabase/transactions.ts
export async function createTransaction(
  transaction: Omit<Transaction, 'id' | 'created_at'>
): Promise<Transaction> {
  const { data, error } = await supabase
    .from('transactions')
    .insert(transaction)
    .select()
    .single()
    
  if (error) throw error
  return data
}

export async function getTransactionsByCategory(
  userId: string,
  category: string,
  dateRange: DateRange
): Promise<Transaction[]> {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('user_id', userId)
    .eq('category', category)
    .gte('date', dateRange.start)
    .lte('date', dateRange.end)
    .order('date', { ascending: false })
    
  if (error) throw error
  return data
}
```

### Real-time Updates Pattern
```typescript
// hooks/use-transactions.ts
export function useRealtimeTransactions(userId: string) {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  
  useEffect(() => {
    const channel = supabase
      .channel('transactions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'transactions',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setTransactions(prev => [payload.new as Transaction, ...prev])
          }
          // Handle UPDATE and DELETE
        }
      )
      .subscribe()
      
    return () => {
      supabase.removeChannel(channel)
    }
  }, [userId])
  
  return transactions
}
```

## AI Integration Workflows

### Transaction Analysis Pipeline
```typescript
// lib/ai/analysis.ts
export async function runTransactionAnalysis(
  userId: string
): Promise<AnalysisResult> {
  // 1. Fetch user transactions
  const transactions = await getTransactionsByUser(userId)
  
  // 2. Categorize uncategorized transactions (MistralAI)
  const categorizedTransactions = await Promise.all(
    transactions
      .filter(t => !t.category)
      .map(t => categorizeTransaction(t.description, t.amount))
  )
  
  // 3. Generate insights (Gemini)
  const insights = await analyzeFinancialData(transactions, {
    timeframe: 'last_3_months',
    goals: await getUserGoals(userId)
  })
  
  // 4. Store results
  const analysisResult = {
    insights,
    recommendations: insights.map(i => i.recommendations).flat(),
    risk_score: calculateRiskScore(transactions),
    created_at: new Date().toISOString()
  }
  
  await storeAnalysisResult(userId, analysisResult)
  
  return analysisResult
}
```

### AI Chat Integration
```typescript
// app/ai-chat/page.tsx
import { ChatInterface } from '@/components/chat'
import { useAIChat } from '@/hooks/use-ai-chat'

export default function AIChatPage() {
  const { messages, sendMessage, isLoading } = useAIChat()
  
  return (
    <div className="container mx-auto max-w-4xl p-6">
      {/* Uses application/modals/ styling for chat container */}
      <ChatInterface
        messages={messages}
        onSendMessage={sendMessage}
        isLoading={isLoading}
        placeholder="Ask me about your finances..."
      />
    </div>
  )
}
```

## Security Integration Patterns

### Row Level Security (RLS)
```sql
-- Supabase RLS policies
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own transactions" ON transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions" ON transactions  
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own transactions" ON transactions
  FOR UPDATE USING (auth.uid() = user_id);
```

### API Key Security
```typescript
// app/api/ai/gemini/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'

export async function POST(request: Request) {
  const supabase = createRouteHandlerClient({ cookies })
  
  // Verify authentication
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    return new Response('Unauthorized', { status: 401 })
  }
  
  // Rate limiting
  const rateLimitResult = await checkRateLimit(session.user.id)
  if (!rateLimitResult.success) {
    return new Response('Rate limit exceeded', { status: 429 })
  }
  
  // Process AI request
  const result = await processGeminiRequest(await request.json())
  return Response.json(result)
}
```

## Error Handling Patterns

### Global Error Boundary
```typescript
// app/error.tsx
'use client'

import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="container mx-auto p-6">
      {/* Uses core-components/alerts/alerts.html styling */}
      <Alert variant="error">
        <h2 className="text-lg font-semibold">Something went wrong!</h2>
        <p className="mt-2">{error.message}</p>
        <Button onClick={reset} className="mt-4">
          Try again
        </Button>
      </Alert>
    </div>
  )
}
```

### Service Error Handling
```typescript
// lib/utils/error-handling.ts
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'AppError'
  }
}

export function handleServiceError(error: unknown): AppError {
  if (error instanceof AppError) return error
  
  if (error instanceof Error) {
    // Handle specific service errors
    if (error.message.includes('Supabase')) {
      return new AppError('Database error', 'DATABASE_ERROR', 500)
    }
    if (error.message.includes('Gemini')) {
      return new AppError('AI service unavailable', 'AI_ERROR', 503)
    }
  }
  
  return new AppError('Unknown error', 'UNKNOWN_ERROR', 500)
}
```

## Performance Integration Patterns

### Caching Strategy
```typescript
// lib/cache/index.ts
import { unstable_cache } from 'next/cache'

export const getCachedTransactions = unstable_cache(
  async (userId: string) => getTransactionsByUser(userId),
  ['user-transactions'],
  {
    tags: [`user-${userId}-transactions`],
    revalidate: 300 // 5 minutes
  }
)

export const getCachedAnalysis = unstable_cache(
  async (userId: string) => getLatestAnalysis(userId),
  ['user-analysis'],
  {
    tags: [`user-${userId}-analysis`],
    revalidate: 3600 // 1 hour
  }
)
```

### Loading States with TailGrids
```typescript
// components/ui/loading-states.tsx
import { Spinner } from '@/components/ui/spinner'
import { Skeleton } from '@/components/ui/skeleton'

export const TransactionLoadingSkeleton = () => (
  <div className="space-y-4">
    {/* Uses core-components/skeletons/ patterns */}
    {Array.from({ length: 5 }).map((_, i) => (
      <Skeleton key={i} className="h-16 w-full" />
    ))}
  </div>
)

export const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-64">
    {/* Uses core-components/spinners/ patterns */}
    <Spinner size="lg" />
    <span className="ml-2">Loading financial data...</span>
  </div>
)
```

This integration pattern ensures all components work seamlessly together while maintaining the benefits of TailGrids' design system and Next.js performance optimizations.
