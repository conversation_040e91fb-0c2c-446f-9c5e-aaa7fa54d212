# Library Context - AI-Powered Personal Finance Tracker

## Library Architecture Overview
The `lib/` directory contains utility functions, service integrations, and shared logic that powers the AI-powered personal finance tracker. All utilities are designed to work seamlessly with the TailGrids component system and maintain type safety.

## Directory Structure

### `/supabase/` - Database Integration
Core Supabase client configuration and database operations:

#### Client Configuration
```typescript
// lib/supabase/client.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from '@/types/database'

export const supabase = createClientComponentClient<Database>()
export const createServerClient = () => createServerComponentClient<Database>({ cookies })
```

#### Database Operations
```typescript
// lib/supabase/transactions.ts
export async function createTransaction(transaction: TransactionInsert): Promise<Transaction> {
  const { data, error } = await supabase
    .from('transactions')
    .insert(transaction)
    .select('*, account(*), category(*)')
    .single()
    
  if (error) throw new DatabaseError(error.message)
  return data
}
```

### `/ai/` - AI Service Integrations
Integration with Google Gemini and MistralAI services:

#### Google Gemini Integration
```typescript
// lib/ai/gemini.ts
import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY!)

export async function analyzeFinancialData(
  transactions: Transaction[],
  context: AnalysisContext
): Promise<FinancialInsight[]> {
  const model = genAI.getGenerativeModel({ model: 'gemini-pro' })
  // Implementation with proper error handling and rate limiting
}
```

#### MistralAI Integration
```typescript
// lib/ai/mistral.ts
export async function categorizeTransaction(
  description: string,
  amount: number
): Promise<TransactionCategory> {
  // Implementation with proper error handling
}
```

### `/providers/` - React Context Providers
Application-wide context providers for state management:

#### Auth Provider
```typescript
// lib/providers/auth-provider.tsx
export const AuthProvider = ({ children }) => {
  const [session, setSession] = useState(null)
  const [loading, setLoading] = useState(true)
  
  // Supabase auth integration
  return (
    <AuthContext.Provider value={{ session, loading }}>
      {children}
    </AuthContext.Provider>
  )
}
```

#### Query Provider
```typescript
// lib/providers/query-provider.tsx
export const QueryProvider = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 5, // 5 minutes
        retry: 1,
      },
    },
  })
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
```

### `/validations/` - Input Validation Schemas
Zod schemas for form and API validation:

```typescript
// lib/validations/transaction.ts
export const transactionSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().min(1, 'Description is required'),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format'),
  categoryId: z.string().uuid().optional(),
})
```

## Utility Functions

### Core Utilities (`utils.ts`)
Essential utility functions used throughout the application:

#### Class Name Utilities
```typescript
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

#### Formatting Utilities
```typescript
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

export function formatDate(date: Date | string): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(typeof date === 'string' ? new Date(date) : date)
}
```

#### Validation Utilities
```typescript
export function isValidEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}
```

#### Performance Utilities
```typescript
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}
```

## Service Integration Patterns

### Database Operations
All database operations follow consistent patterns:

```typescript
// Error handling pattern
export async function performDatabaseOperation<T>(
  operation: () => Promise<{ data: T | null; error: any }>
): Promise<T> {
  try {
    const { data, error } = await operation()
    if (error) throw new DatabaseError(error.message)
    if (!data) throw new Error('No data returned')
    return data
  } catch (error) {
    handleServiceError(error)
    throw error
  }
}

// Real-time subscription pattern
export function useRealtimeSubscription<T>(
  table: string,
  filter: string,
  callback: (payload: T) => void
) {
  useEffect(() => {
    const channel = supabase
      .channel(table)
      .on('postgres_changes', { event: '*', schema: 'public', table, filter }, callback)
      .subscribe()
      
    return () => supabase.removeChannel(channel)
  }, [table, filter])
}
```

### AI Service Integration
Consistent patterns for AI service integration:

```typescript
// Rate limiting pattern
export class AIServiceManager {
  private requestQueue: Array<() => Promise<any>> = []
  private isProcessing = false
  
  async processRequest<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await request()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      this.processQueue()
    })
  }
}

// Caching pattern
export const aiResponseCache = new Map<string, { data: any; timestamp: number }>()

export async function getCachedAIResponse<T>(
  key: string,
  generator: () => Promise<T>,
  ttl: number = 300000 // 5 minutes
): Promise<T> {
  const cached = aiResponseCache.get(key)
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  const data = await generator()
  aiResponseCache.set(key, { data, timestamp: Date.now() })
  return data
}
```

## Error Handling

### Service Error Handling
```typescript
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'AppError'
  }
}

export function handleServiceError(error: unknown): AppError {
  if (error instanceof AppError) return error
  
  if (error instanceof Error) {
    if (error.message.includes('Supabase')) {
      return new AppError('Database error', 'DATABASE_ERROR', 500)
    }
    if (error.message.includes('Gemini')) {
      return new AppError('AI service unavailable', 'AI_ERROR', 503)
    }
  }
  
  return new AppError('Unknown error', 'UNKNOWN_ERROR', 500)
}
```

### Error Recovery Patterns
```typescript
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      if (attempt === maxRetries) throw error
      await sleep(delay * attempt)
    }
  }
  throw new Error('Max retries exceeded')
}
```

## Performance Optimization

### Caching Strategies
```typescript
// Memory cache for frequently accessed data
export const memoryCache = new Map<string, { data: any; expiry: number }>()

export function getCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 300000
): Promise<T> {
  const cached = memoryCache.get(key)
  if (cached && Date.now() < cached.expiry) {
    return Promise.resolve(cached.data)
  }
  
  return fetcher().then(data => {
    memoryCache.set(key, { data, expiry: Date.now() + ttl })
    return data
  })
}
```

### Database Query Optimization
```typescript
// Pagination helper
export function createPaginationQuery(
  page: number,
  pageSize: number = 10
) {
  const offset = (page - 1) * pageSize
  return { offset, limit: pageSize }
}

// Bulk operations
export async function bulkInsert<T>(
  table: string,
  records: Partial<T>[],
  batchSize: number = 100
): Promise<T[]> {
  const results: T[] = []
  
  for (let i = 0; i < records.length; i += batchSize) {
    const batch = records.slice(i, i + batchSize)
    const { data, error } = await supabase
      .from(table)
      .insert(batch)
      .select()
    
    if (error) throw error
    results.push(...data)
  }
  
  return results
}
```

## Testing Utilities

### Mock Factories
```typescript
// lib/testing/factories.ts
export const createMockTransaction = (overrides?: Partial<Transaction>): Transaction => ({
  id: generateId(),
  userId: 'user-123',
  amount: 100.00,
  description: 'Test transaction',
  date: new Date().toISOString(),
  type: 'expense',
  createdAt: new Date().toISOString(),
  ...overrides,
})
```

### Test Helpers
```typescript
export const setupTestDatabase = async () => {
  // Set up test database state
}

export const cleanupTestDatabase = async () => {
  // Clean up test database state
}
```

## Security Considerations

### Input Sanitization
```typescript
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000) // Limit length
}
```

### API Key Management
```typescript
export function validateApiKey(key: string, service: string): boolean {
  const patterns = {
    gemini: /^AIza[0-9A-Za-z-_]{35}$/,
    mistral: /^[a-f0-9]{32}$/,
  }
  
  return patterns[service]?.test(key) ?? false
}
```

This library architecture provides a solid foundation for the AI-powered personal finance tracker while maintaining integration with TailGrids components and ensuring type safety throughout the application.
