# AI-Powered Personal Finance Tracker

A comprehensive AI-powered personal finance management application built with the Claude Code Development Kit framework, integrating TailGrids UI components, Next.js 14, Supabase, and multiple AI services.

## 🚀 Framework Overview

This project implements the **Claude Code Development Kit** - an integrated system that transforms Claude Code into an orchestrated development environment through:

- **Documentation System**: 3-tier structured context that auto-loads based on task requirements
- **Command Templates**: Orchestration patterns for multi-agent workflows
- **MCP Servers**: External AI services providing current documentation and consultation
- **TailGrids Integration**: 600+ pre-built UI components for rapid development

## 🏗 Architecture

### Core Technologies
- **Frontend**: Next.js 14 with App Router + TypeScript
- **UI Framework**: TailGrids (600+ components) + Tailwind CSS v4
- **Database**: Supabase (PostgreSQL + Auth + Storage)
- **AI Services**: Google Gemini API + MistralAI API
- **Development**: Claude Code Development Kit
- **Deployment**: Vercel + Supabase

### Project Structure
```
├── .claude/
│   └── commands/           # AI orchestration templates
├── docs/
│   ├── ai-context/        # Tier 1 - Foundation documentation
│   ├── open-issues/       # Issue tracking templates
│   └── specs/             # Feature specifications
├── app/                   # Next.js 14 App Router
├── components/            # Custom React components + TailGrids integration
├── lib/                   # Utility functions and service integrations
├── types/                 # TypeScript definitions
├── hooks/                 # Custom React hooks
└── [TailGrids Components] # 600+ UI components preserved
```

## 🛠 Installation & Setup

### Prerequisites
- Node.js 18+ and npm 9+
- Claude Code installed and configured
- Supabase account and project
- Google Gemini API key (optional)
- MistralAI API key (optional)

### Quick Start

1. **Clone and Install Dependencies**
   ```bash
   git clone <repository-url>
   cd ai-finance-tracker
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys and configuration
   ```

3. **Database Setup**
   ```bash
   # Start local Supabase (optional)
   npx supabase start
   
   # Generate TypeScript types
   npm run supabase:gen-types
   ```

4. **Development Server**
   ```bash
   npm run dev
   ```

5. **Claude Code Integration**
   ```bash
   # Initialize Claude Code in project directory
   claude
   
   # Use specialized commands
   /full-context
   /code-review
   /update-docs
   ```

## 🎯 Claude Code Development Kit Features

### Automated Command Templates

#### `/full-context` - Complete Project Analysis
Provides comprehensive analysis including:
- Project structure and architecture review
- TailGrids component integration status
- Database schema and API configurations
- Development state assessment

#### `/code-review` - Automated Code Review
Performs thorough code review covering:
- TypeScript implementation and type safety
- Security vulnerabilities and best practices
- Performance analysis and optimizations
- TailGrids integration validation

#### `/update-docs` - Documentation Maintenance
Automatically updates:
- Master context files (CLAUDE.md)
- Component-level documentation
- Feature-specific context files
- Integration patterns and examples

#### `/test-suite` - Comprehensive Testing
Executes and analyzes:
- Unit tests for utilities and components
- Integration tests for AI services
- E2E tests for user workflows
- Performance and accessibility testing

#### `/deploy-check` - Pre-deployment Validation
Validates deployment readiness:
- Build and environment configuration
- Security and performance checks
- Database migration status
- AI service connectivity

### 3-Tier Documentation System

#### Tier 1: Foundation (Auto-loaded)
- `docs/ai-context/project-structure.md` - Complete stack information
- `docs/ai-context/system-integration.md` - Integration patterns
- `docs/ai-context/deployment-infrastructure.md` - Infrastructure context
- `CLAUDE.md` - Master AI context and coding standards

#### Tier 2: Component Context
- Component-level `CLAUDE.md` files with specific patterns
- Library and utility documentation
- Service integration guidelines

#### Tier 3: Feature Context
- Feature-specific implementation details
- Route-specific patterns and requirements
- API endpoint documentation

## 🔧 Development Workflow

### Standard Development Process
1. **Planning**: Create GitHub issues using templates in `docs/open-issues/`
2. **Context Loading**: Claude Code automatically loads relevant documentation
3. **Implementation**: Follow TDD with TailGrids component integration
4. **Review**: Use `/code-review` command for automated analysis
5. **Testing**: Execute `/test-suite` for comprehensive validation
6. **Documentation**: Use `/update-docs` to maintain currency
7. **Deployment**: Run `/deploy-check` before deployment

### AI-Assisted Development
```bash
# Get comprehensive project analysis
claude /full-context

# Review current code changes
claude /code-review

# Generate new TailGrids-integrated component
claude /create-component "financial summary card with chart integration"

# Perform AI-powered analysis
claude /ai-analysis "analyze spending patterns for the last 3 months"

# Update all documentation
claude /update-docs

# Validate deployment readiness
claude /deploy-check
```

## 🎨 TailGrids Integration

### Component Usage
The project integrates 600+ TailGrids components:

```typescript
// Use existing TailGrids components
import { Button } from '@/core-components/buttons/button'
import { Card } from '@/application/cards/card-01'
import { Input } from '@/core-components/inputs/input'

// Extended components with additional functionality
import { ExtendedButton } from '@/components/ui/button'
import { FinanceCard } from '@/components/ui/finance-card'
```

### Styling Integration
```css
/* app/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;
@plugin 'tailgrids/plugin';  /* TailGrids integration */
```

### Component Creation Pattern
```bash
# Use Claude Code to create new components
claude /create-component "transaction list with filtering and TailGrids styling"
```

## 🤖 AI Services Integration

### Google Gemini API
- Complex financial analysis and insights
- Spending pattern recognition
- Budget recommendations
- Risk assessments

### MistralAI API
- Transaction categorization
- Natural language processing
- Financial query handling
- Data extraction from descriptions

### Usage Examples
```typescript
// Financial analysis with Gemini
const insights = await analyzeFinancialData(transactions, context)

// Transaction categorization with MistralAI
const category = await categorizeTransaction(description, amount)
```

## 📊 Application Features

### Core Functionality
- **Setup & Onboarding**: Multi-step configuration with API testing
- **Dashboard**: Real-time financial overview with AI insights
- **Transaction Management**: Advanced categorization and tracking
- **AI Chat Interface**: Natural language financial assistance
- **Analysis & Insights**: AI-powered financial health assessment
- **Recurring Payments**: Subscription and bill tracking
- **Loans & Debits**: Debt management and payment scheduling

### AI-Powered Features
- Automated transaction categorization
- Spending pattern analysis
- Budget recommendations
- Financial risk assessment
- Natural language query processing
- Predictive financial modeling

## 🧪 Testing

### Test Execution
```bash
# Run all tests
npm test

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage

# Claude Code test suite
claude /test-suite
```

### Testing Strategy
- **Unit Tests**: Component logic and utilities
- **Integration Tests**: API endpoints and services
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Load times and responsiveness
- **Accessibility Tests**: WCAG compliance

## 🚀 Deployment

### Environment Setup
```bash
# Production environment variables
NEXT_PUBLIC_SUPABASE_URL=your_production_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_key
GOOGLE_GEMINI_API_KEY=your_production_gemini_key
MISTRAL_AI_API_KEY=your_production_mistral_key
```

### Deployment Process
```bash
# Validate deployment readiness
claude /deploy-check

# Build and deploy
npm run build
```

### MCP Server Configuration
```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["@upstash/context7"]
    },
    "gemini": {
      "command": "npx", 
      "args": ["mcp-gemini-assistant"],
      "env": {
        "GEMINI_API_KEY": "your-api-key"
      }
    }
  }
}
```

## 📚 Documentation

### Key Documentation Files
- `CLAUDE.md` - Master AI context and development standards
- `docs/ai-context/` - Foundation documentation for AI assistance
- `docs/specs/` - Feature specifications and requirements
- Component `CLAUDE.md` files - Context-specific development guidance

### Documentation Maintenance
```bash
# Update all documentation automatically
claude /update-docs

# View documentation system overview
claude /docs-overview
```

## 🤝 Contributing

### Development Guidelines
1. Follow the established 3-tier documentation structure
2. Use TailGrids components whenever possible
3. Implement proper TypeScript typing
4. Write comprehensive tests
5. Update documentation with changes
6. Use Claude Code commands for consistency

### Code Review Process
```bash
# Automated code review before commits
claude /code-review

# Manual review checklist:
# - TailGrids integration properly implemented
# - TypeScript types are comprehensive
# - Tests cover new functionality
# - Documentation is updated
# - Security best practices followed
```

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

### Getting Help
1. Check the documentation in `docs/`
2. Use Claude Code commands for analysis
3. Review component `CLAUDE.md` files
4. Consult the TailGrids component library

### Common Issues
- **Environment Variables**: Ensure all required keys are configured
- **Database Connection**: Verify Supabase configuration
- **AI Services**: Check API key validity and quotas
- **TailGrids**: Ensure plugin is properly configured in Tailwind

---

Built with the Claude Code Development Kit framework for maximum AI-assisted development efficiency.
