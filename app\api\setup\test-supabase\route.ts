import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { url, anon<PERSON><PERSON> } = body

    // Validate required parameters
    if (!url || !anonKey) {
      return NextResponse.json(
        { success: false, error: 'URL and anon key are required' },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(url)
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid Supabase URL format' },
        { status: 400 }
      )
    }

    // Test connection by creating a client and making a simple query
    const testClient = createClient(url, anonKey)
    
    // Try to access the database (this will fail gracefully if credentials are wrong)
    const { error } = await testClient
      .from('_test_connection')
      .select('*')
      .limit(1)

    // If we get a specific error about the table not existing, that's actually good
    // It means we connected successfully but the table doesn't exist
    if (error && error.message.includes('relation "_test_connection" does not exist')) {
      return NextResponse.json({ success: true })
    }

    // If we get an auth error, the credentials are wrong
    if (error && (
      error.message.includes('Invalid API key') ||
      error.message.includes('JWT') ||
      error.message.includes('unauthorized')
    )) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Any other error or success means connection worked
    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Supabase connection test error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Connection test failed' 
      },
      { status: 500 }
    )
  }
}
