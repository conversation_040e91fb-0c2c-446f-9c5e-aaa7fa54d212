<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Toast | TailGrids</title>
  <link rel="shortcut icon" href="../../assets/images/favicon.svg" type="image/x-icon" />
  <link rel="stylesheet" href="../../assets/css/tailwind.css" />
</head>

<body>
  <!-- ====== Toast Section Start -->
  <section class="bg-gray-2 py-[60px] dark:bg-dark">
    <div class="mx-auto px-4 sm:container">
      <div
        class="relative max-w-[520px] items-center justify-between rounded-lg border border-stroke bg-white px-5 py-4 shadow-1 sm:flex dark:border-dark-3 dark:bg-dark-2 dark:shadow-box-dark">
        <div class="mb-4 flex items-center sm:mb-0">
          <div class="mr-[18px] h-[54px] w-full max-w-[54px] overflow-hidden rounded-full">
            <img src="../images/toast/image-01.png" alt="user image"
              class="h-full w-full rounded-full object-cover object-center" />
          </div>
          <div class="w-full">
            <h6 class="mb-0.5 text-base font-semibold text-dark sm:text-lg dark:text-white">
              Devid Miller
            </h6>
            <p class="text-sm text-body-color dark:text-dark-6">
              Started following your
            </p>
          </div>
        </div>
        <div class="flex items-center">
          <button class="mr-3 rounded-[3px] bg-primary px-4 py-[5px] text-xs font-medium text-white hover:bg-blue-dark">
            Accept
          </button>
          <button class="mr-8 rounded-[3px] bg-dark px-4 py-[5px] text-xs font-medium text-white hover:bg-dark/90">
            Decline
          </button>
          <button class="absolute right-[18px] top-1/2 -translate-y-1/2 text-dark-5 hover:text-red dark:text-dark-6">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
              class="fill-current">
              <g clip-path="url(#clip0_1088_26057)">
                <path
                  d="M8.79999 7.99999L14.9 1.89999C15.125 1.67499 15.125 1.32499 14.9 1.09999C14.675 0.874994 14.325 0.874994 14.1 1.09999L7.99999 7.19999L1.89999 1.09999C1.67499 0.874994 1.32499 0.874994 1.09999 1.09999C0.874994 1.32499 0.874994 1.67499 1.09999 1.89999L7.19999 7.99999L1.09999 14.1C0.874994 14.325 0.874994 14.675 1.09999 14.9C1.19999 15 1.34999 15.075 1.49999 15.075C1.64999 15.075 1.79999 15.025 1.89999 14.9L7.99999 8.79999L14.1 14.9C14.2 15 14.35 15.075 14.5 15.075C14.65 15.075 14.8 15.025 14.9 14.9C15.125 14.675 15.125 14.325 14.9 14.1L8.79999 7.99999Z" />
              </g>
              <defs>
                <clipPath id="clip0_1088_26057">
                  <rect width="16" height="16" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </section>
  <!-- ====== Toast Section End -->
</body>

</html>