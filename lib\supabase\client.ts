import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import type { Database } from '@/types/database'

/**
 * Client-side Supabase client for use in React components
 */
export const createClient = () => createClientComponentClient<Database>()

/**
 * Server-side Supabase client for use in Server Components and API routes
 */
export const createServerClient = () => 
  createServerComponentClient<Database>({ cookies })

/**
 * Supabase client instance for client-side operations
 */
export const supabase = createClient()
