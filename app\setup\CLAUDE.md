# Setup Flow Context - AI-Powered Personal Finance Tracker

## Setup Flow Overview
The setup flow is the critical onboarding experience that configures API services, user profiles, and preferences for the AI-powered personal finance tracker. This multi-step process ensures proper integration with Supabase, AI services, and TailGrids components.

## Flow Architecture

### Step 1: API Configuration (`/setup/api-config`)
**Purpose**: Configure and validate essential API connections
**Components Used**: TailGrids Cards, Inputs, Buttons, Progress Bars, Alerts

#### Key Features
- **Supabase Configuration**: Database URL and authentication key setup
- **Google Gemini Setup**: Optional AI service for advanced analytics
- **MistralAI Setup**: Optional AI service for transaction categorization
- **Real-time Validation**: Test connections before proceeding
- **Error Handling**: Clear feedback for configuration issues

#### Implementation Pattern
```typescript
// API testing with immediate feedback
const testSupabaseConnection = async () => {
  setIsLoading(true)
  try {
    const response = await fetch('/api/setup/test-supabase', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: formData.supabaseUrl,
        anonKey: formData.supabaseAnonKey
      })
    })
    
    const result = await response.json()
    setTestResults(prev => ({ ...prev, supabase: result.success }))
    
    if (!result.success) {
      setErrors(prev => ({ ...prev, supabase: result.error }))
    }
  } catch (error) {
    setTestResults(prev => ({ ...prev, supabase: false }))
    setErrors(prev => ({ ...prev, supabase: 'Network error' }))
  } finally {
    setIsLoading(false)
  }
}
```

#### TailGrids Integration
- **Progress Bar**: `core-components/progress-bars/progress-bars.html` at 33%
- **Card Container**: `application/cards/card-01.html` for form wrapper
- **Input Fields**: `core-components/inputs/input.html` with validation styling
- **Buttons**: `core-components/buttons/buttons.html` with success/error states
- **Alerts**: `core-components/alerts/alerts.html` for error messages

### Step 2: Profile Setup (`/setup/profile-setup`)
**Purpose**: Collect user information, preferences, and security settings
**Components Used**: TailGrids File Uploads, Switches, Selects, Radio Buttons

#### Key Features
- **Personal Information**: Name, email, profile picture upload
- **Security Setup**: Password creation with strength validation
- **Financial Preferences**: Currency, date format, income range
- **AI Preferences**: Response detail level, personality, learning settings
- **Terms and Conditions**: Agreement validation

#### Implementation Pattern
```typescript
// Profile setup with comprehensive validation
const handleProfileSubmit = async (data: ProfileFormData) => {
  const validation = profileSchema.safeParse(data)
  if (!validation.success) {
    setErrors(validation.error.flatten().fieldErrors)
    return
  }
  
  try {
    // Create user profile in Supabase
    const { error } = await supabase.auth.signUp({
      email: data.email,
      password: data.password,
      options: {
        data: {
          firstName: data.firstName,
          lastName: data.lastName,
          currency: data.currency,
          preferences: {
            aiResponseDetail: data.aiResponseDetail,
            aiPersonality: data.aiPersonality,
            aiLearning: data.aiLearning,
          }
        }
      }
    })
    
    if (error) throw error
    router.push('/setup/setup-complete')
  } catch (error) {
    setGlobalError(error.message)
  }
}
```

#### TailGrids Integration
- **Progress Bar**: 66% completion
- **File Upload**: `core-components/file-uploads/file-upload-02.html` for avatar
- **Avatar Preview**: `core-components/avatars/avatars-01.html`
- **Form Inputs**: `core-components/inputs/input.html` with labels
- **Switches**: `core-components/switches/switch.html` for toggles
- **Select Dropdowns**: `core-components/select/select.html` for options
- **Radio Groups**: Custom implementation using TailGrids styling
- **Button Groups**: `core-components/button-groups/button-group-02.html`

### Step 3: Setup Completion (`/setup/setup-complete`)
**Purpose**: Confirm successful setup and provide next steps
**Components Used**: TailGrids Lists, Badges, Buttons

#### Key Features
- **Setup Summary**: Visual confirmation of completed configuration
- **Success Indicators**: Check marks for each completed step
- **Navigation**: Direct link to main dashboard
- **Onboarding Tips**: Optional quick start guide

#### TailGrids Integration
- **Progress Bar**: 100% completion
- **List Components**: `core-components/lists/lists.html` with check icons
- **Success Badges**: `core-components/badges/badges.html` for status
- **CTA Button**: `core-components/buttons/buttons.html` primary style

## Form Validation Strategy

### Client-Side Validation
```typescript
// Zod schemas for type-safe validation
export const apiConfigSchema = z.object({
  supabaseUrl: z.string().url('Invalid Supabase URL'),
  supabaseAnonKey: z.string().min(1, 'Supabase key is required'),
  geminiApiKey: z.string().optional(),
  mistralApiKey: z.string().optional(),
})

export const profileSetupSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  currency: z.enum(['USD', 'EUR', 'GBP', 'CAD']),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to terms'),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})
```

### Server-Side Validation
```typescript
// API route validation
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validation = apiConfigSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validation.error },
        { status: 400 }
      )
    }
    
    // Process validated data
  } catch (error) {
    return NextResponse.json(
      { error: 'Server error' },
      { status: 500 }
    )
  }
}
```

## State Management

### Local State Pattern
```typescript
// Setup flow state management
const useSetupFlow = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<SetupFormData>({
    // Initial form state
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  
  const updateFormData = (stepData: Partial<SetupFormData>) => {
    setFormData(prev => ({ ...prev, ...stepData }))
  }
  
  const validateStep = (step: number): boolean => {
    // Step-specific validation logic
    return true
  }
  
  return {
    currentStep,
    formData,
    errors,
    isLoading,
    updateFormData,
    validateStep,
    nextStep: () => setCurrentStep(prev => prev + 1),
    prevStep: () => setCurrentStep(prev => prev - 1),
  }
}
```

### Persistence Strategy
```typescript
// Save progress to localStorage for recovery
const saveSetupProgress = (step: number, data: Partial<SetupFormData>) => {
  const setupState = {
    step,
    data,
    timestamp: Date.now(),
  }
  localStorage.setItem('setupProgress', JSON.stringify(setupState))
}

const loadSetupProgress = (): { step: number; data: Partial<SetupFormData> } | null => {
  const saved = localStorage.getItem('setupProgress')
  if (!saved) return null
  
  const state = JSON.parse(saved)
  // Check if progress is less than 24 hours old
  if (Date.now() - state.timestamp > 24 * 60 * 60 * 1000) {
    localStorage.removeItem('setupProgress')
    return null
  }
  
  return state
}
```

## API Integration Testing

### Connection Testing Endpoints
```typescript
// /app/api/setup/test-supabase/route.ts
export async function POST(request: Request) {
  try {
    const { url, anonKey } = await request.json()
    
    const testClient = createClient(url, anonKey)
    const { error } = await testClient.from('_test').select('*').limit(1)
    
    return NextResponse.json({ 
      success: !error,
      error: error?.message 
    })
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: 'Connection failed' 
    })
  }
}

// /app/api/setup/test-gemini/route.ts
export async function POST(request: Request) {
  try {
    const { apiKey } = await request.json()
    
    const genAI = new GoogleGenerativeAI(apiKey)
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' })
    
    await model.generateContent('Test connection')
    
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: 'Invalid API key' 
    })
  }
}
```

## Security Considerations

### Input Sanitization
```typescript
const sanitizeApiConfig = (config: any) => ({
  supabaseUrl: sanitizeUrl(config.supabaseUrl),
  supabaseAnonKey: sanitizeString(config.supabaseAnonKey),
  geminiApiKey: config.geminiApiKey ? sanitizeString(config.geminiApiKey) : undefined,
  mistralApiKey: config.mistralApiKey ? sanitizeString(config.mistralApiKey) : undefined,
})
```

### Secure Storage
```typescript
// Encrypt sensitive data before storage
const encryptApiKeys = (keys: ApiKeys): string => {
  // Use appropriate encryption for sensitive data
  return btoa(JSON.stringify(keys)) // Simplified for example
}
```

## Error Handling

### User-Friendly Error Messages
```typescript
const getErrorMessage = (error: string): string => {
  const errorMap = {
    'Invalid login credentials': 'Email or password is incorrect',
    'User already registered': 'An account with this email already exists',
    'Network error': 'Please check your internet connection',
    'Invalid API key': 'The API key you entered is not valid',
  }
  
  return errorMap[error] || 'An unexpected error occurred'
}
```

### Recovery Mechanisms
```typescript
const handleSetupError = (error: Error, step: number) => {
  // Log error for debugging
  console.error('Setup error:', error)
  
  // Show user-friendly message
  setGlobalError(getErrorMessage(error.message))
  
  // Allow retry or recovery
  setRetryable(true)
  
  // Save current progress
  saveSetupProgress(step, formData)
}
```

## Testing Strategy

### Unit Testing
```typescript
describe('Setup Flow', () => {
  it('validates API configuration correctly', () => {
    const validConfig = {
      supabaseUrl: 'https://test.supabase.co',
      supabaseAnonKey: 'valid-key',
    }
    
    const result = apiConfigSchema.safeParse(validConfig)
    expect(result.success).toBe(true)
  })
  
  it('handles invalid Supabase URL', () => {
    const invalidConfig = {
      supabaseUrl: 'invalid-url',
      supabaseAnonKey: 'valid-key',
    }
    
    const result = apiConfigSchema.safeParse(invalidConfig)
    expect(result.success).toBe(false)
  })
})
```

### Integration Testing
```typescript
describe('Setup API Integration', () => {
  it('tests Supabase connection successfully', async () => {
    const response = await fetch('/api/setup/test-supabase', {
      method: 'POST',
      body: JSON.stringify({
        url: process.env.TEST_SUPABASE_URL,
        anonKey: process.env.TEST_SUPABASE_KEY,
      }),
    })
    
    const result = await response.json()
    expect(result.success).toBe(true)
  })
})
```

This setup flow provides a comprehensive onboarding experience that properly configures all necessary services while maintaining a smooth user experience with TailGrids components.
