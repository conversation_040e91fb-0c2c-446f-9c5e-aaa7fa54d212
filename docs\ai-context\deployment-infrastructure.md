# Deployment Infrastructure - AI-Powered Personal Finance Tracker

## Overview
This document outlines the complete deployment infrastructure for the AI-powered personal finance tracker, including development, staging, and production environments.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│                 │    │                 │    │                 │
│ Local Machine   │───▶│ Vercel Preview  │───▶│ Vercel Production│
│ + Local Supabase│    │ + Staging DB    │    │ + Production DB │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Environment Configuration

### Development Environment
**Target**: Local development with hot reloading
**Infrastructure**: 
- Next.js dev server (localhost:3000)
- Local Supabase instance or development database
- Local file system for TailGrids components

```env
# .env.local (Development)
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_local_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_local_service_role_key

GOOGLE_GEMINI_API_KEY=your_gemini_dev_key
MISTRAL_AI_API_KEY=your_mistral_dev_key

NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_local_secret

NODE_ENV=development
```

**Setup Commands**:
```bash
# Install dependencies
npm install

# Start Supabase locally (optional)
npx supabase start

# Generate TypeScript types from Supabase
npm run supabase:gen-types

# Start development server
npm run dev
```

### Staging Environment  
**Target**: Pre-production testing and preview
**Infrastructure**: 
- Vercel Preview deployment
- Supabase staging project
- AI services with development quotas

```env
# Environment Variables in Vercel (Staging)
NEXT_PUBLIC_SUPABASE_URL=https://your-staging-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=staging_anon_key
SUPABASE_SERVICE_ROLE_KEY=staging_service_role_key

GOOGLE_GEMINI_API_KEY=gemini_staging_key
MISTRAL_AI_API_KEY=mistral_staging_key

NEXTAUTH_URL=https://your-app-staging.vercel.app
NEXTAUTH_SECRET=staging_secret

NODE_ENV=production
VERCEL_ENV=preview
```

### Production Environment
**Target**: Live application for end users
**Infrastructure**:
- Vercel Production deployment
- Supabase production project
- AI services with production quotas
- CDN for static assets

```env
# Environment Variables in Vercel (Production)
NEXT_PUBLIC_SUPABASE_URL=https://your-production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=production_anon_key
SUPABASE_SERVICE_ROLE_KEY=production_service_role_key

GOOGLE_GEMINI_API_KEY=gemini_production_key
MISTRAL_AI_API_KEY=mistral_production_key

NEXTAUTH_URL=https://your-app.vercel.app
NEXTAUTH_SECRET=production_secret

NODE_ENV=production
VERCEL_ENV=production
```

## Deployment Workflows

### Vercel Configuration
```javascript
// vercel.json
{
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["iad1", "sfo1"],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key"
  },
  "build": {
    "env": {
      "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role",
      "GOOGLE_GEMINI_API_KEY": "@gemini-api-key",
      "MISTRAL_AI_API_KEY": "@mistral-api-key"
    }
  },
  "functions": {
    "app/api/**": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options", 
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### Next.js Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@google/generative-ai']
  },
  images: {
    domains: ['your-supabase-project.supabase.co'],
    formats: ['image/webp', 'image/avif']
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  async redirects() {
    return [
      {
        source: '/dashboard',
        destination: '/',
        permanent: true,
      },
    ]
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: 'https://your-app.vercel.app'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,POST,PUT,DELETE,OPTIONS'
          }
        ]
      }
    ]
  }
}

module.exports = nextConfig
```

## Database Deployment

### Supabase Migration Strategy
```sql
-- migrations/001_initial_schema.sql
-- Create core tables for the finance tracker

-- Users profile extension
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  currency TEXT DEFAULT 'USD',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Accounts table
CREATE TABLE accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'investment')),
  balance DECIMAL(12,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  color TEXT DEFAULT '#3B82F6',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
  category_id UUID REFERENCES categories(id),
  amount DECIMAL(12,2) NOT NULL,
  description TEXT NOT NULL,
  date DATE NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can view own accounts" ON accounts
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own categories" ON categories
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own transactions" ON transactions
  FOR ALL USING (auth.uid() = user_id);
```

### Database Deployment Commands
```bash
# Development
supabase db reset
supabase db push

# Staging
supabase db push --project-ref your-staging-ref

# Production  
supabase db push --project-ref your-production-ref
```

## CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Vercel

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run TypeScript check
        run: npm run type-check
        
      - name: Run linting
        run: npm run lint
        
      - name: Run tests
        run: npm test
        
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          scope: ${{ secrets.VERCEL_ORG_ID }}

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          scope: ${{ secrets.VERCEL_ORG_ID }}
```

## Monitoring and Observability

### Application Monitoring
```typescript
// lib/monitoring/index.ts
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

// Error tracking
export function captureError(error: Error, context?: Record<string, any>) {
  if (process.env.NODE_ENV === 'production') {
    // Send to error tracking service
    console.error('Application error:', error, context)
  }
}

// Performance monitoring
export function trackPerformance(metric: string, value: number) {
  if (process.env.NODE_ENV === 'production') {
    // Track performance metrics
    console.log(`Performance metric ${metric}:`, value)
  }
}
```

### Health Check Endpoints
```typescript
// app/api/health/route.ts
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET() {
  try {
    // Check database connectivity
    const supabase = createServerComponentClient({ cookies })
    const { error } = await supabase.from('profiles').select('id').limit(1)
    
    if (error) throw error
    
    // Check AI services
    const geminiHealthy = await checkGeminiHealth()
    const mistralHealthy = await checkMistralHealth()
    
    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'healthy',
        gemini: geminiHealthy ? 'healthy' : 'degraded',
        mistral: mistralHealthy ? 'healthy' : 'degraded'
      }
    })
  } catch (error) {
    return Response.json(
      { 
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
```

## Security Configuration

### Content Security Policy
```typescript
// middleware.ts - Security headers
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  
  // Security headers
  response.headers.set('X-DNS-Prefetch-Control', 'on')
  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  
  // CSP
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
    "frame-src 'none'"
  ].join('; ')
  
  response.headers.set('Content-Security-Policy', csp)
  
  return response
}
```

### Environment Security
```typescript
// lib/config/environment.ts
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY', 
  'SUPABASE_SERVICE_ROLE_KEY',
  'GOOGLE_GEMINI_API_KEY',
  'MISTRAL_AI_API_KEY',
  'NEXTAUTH_SECRET'
] as const

export function validateEnvironment() {
  const missing = requiredEnvVars.filter(
    envVar => !process.env[envVar]
  )
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}`
    )
  }
}

// Validate on app start
validateEnvironment()
```

## Deployment Checklist

### Pre-deployment Validation
```bash
# Run the deployment check command
claude /deploy-check

# Manual checklist:
# ✅ All tests passing
# ✅ TypeScript compilation successful  
# ✅ ESLint checks passing
# ✅ Environment variables configured
# ✅ Database migrations applied
# ✅ TailGrids CSS compiled correctly
# ✅ AI API keys validated
# ✅ Security headers configured
# ✅ Performance budget met
# ✅ Accessibility standards met
```

### Post-deployment Verification
```bash
# Health check
curl https://your-app.vercel.app/api/health

# Performance audit
npm run lighthouse

# Security scan
npm audit --audit-level=moderate
```

## Scaling Considerations

### Database Scaling
- **Connection Pooling**: Supabase handles automatically
- **Read Replicas**: Available in Supabase Pro
- **Backup Strategy**: Daily automated backups
- **Data Retention**: Configure based on compliance needs

### API Rate Limiting
```typescript
// lib/rate-limiting/index.ts
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
})

export const ratelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(10, '1 m'), // 10 requests per minute
  analytics: true,
})
```

### CDN Configuration
```javascript
// vercel.json - Edge caching
{
  "headers": [
    {
      "source": "/assets/**",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

This deployment infrastructure ensures a robust, scalable, and secure environment for the AI-powered personal finance tracker across all deployment stages.
