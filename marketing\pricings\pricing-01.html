<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Pricing | TailGrids</title>
  <link rel="shortcut icon" href="../../assets/images/favicon.svg" type="image/x-icon" />
  <link rel="stylesheet" href="../../assets/css/tailwind.css" />
</head>

<body>
  <!-- ====== Pricing Section Start -->
  <section class="relative z-10 overflow-hidden bg-white pb-12 pt-20 lg:pb-[90px] lg:pt-[120px] dark:bg-dark">
    <div class="container mx-auto">
      <div class="-mx-4 flex flex-wrap">
        <div class="w-full px-4">
          <div class="mx-auto mb-[60px] max-w-[510px] text-center">
            <span class="mb-2 block text-lg font-semibold text-primary">
              Pricing Table
            </span>
            <h2 class="mb-3 text-3xl font-bold leading-[1.208] text-dark sm:text-4xl md:text-[40px] dark:text-white">
              Our Pricing Plan
            </h2>
            <p class="text-base text-body-color dark:text-dark-6">
              There are many variations of passages of Lorem Ipsum available
              but the majority have suffered alteration in some form.
            </p>
          </div>
        </div>
      </div>

      <div class="-mx-4 flex flex-wrap justify-center">
        <div class="w-full px-4 md:w-1/2 lg:w-1/3">
          <div
            class="relative z-10 mb-10 overflow-hidden rounded-[10px] border-2 border-stroke bg-white px-8 py-10 shadow-pricing sm:p-12 lg:px-6 lg:py-10 xl:p-[50px] dark:border-dark-3 dark:bg-dark-2">
            <span class="mb-3 block text-lg font-semibold text-primary">
              Personal
            </span>
            <h2 class="mb-5 text-[42px] font-bold text-dark dark:text-white">
              <span>$59</span>
              <span class="text-base font-medium text-body-color dark:text-dark-6">
                / year
              </span>
            </h2>
            <p class="mb-8 border-b border-stroke pb-8 text-base text-body-color dark:border-dark-3 dark:text-dark-6">
              Perfect for using in a personal website or a client project.
            </p>
            <div class="mb-9 flex flex-col gap-[14px]">
              <p class="text-base text-body-color dark:text-dark-6">1 User</p>
              <p class="text-base text-body-color dark:text-dark-6">
                All UI components
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Lifetime access
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Free updates
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Use on 1 (one) project
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                3 Months support
              </p>
            </div>
            <a href="javascript:void(0)"
              class="block w-full rounded-md border border-stroke bg-transparent p-3 text-center text-base font-medium text-primary transition hover:border-primary hover:bg-primary hover:text-white dark:border-dark-3">
              Choose Personal
            </a>

            <div>
              <span class="absolute right-0 top-7 z-[-1]">
                <svg width="77" height="172" viewBox="0 0 77 172" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="86" cy="86" r="86" fill="url(#paint0_linear)" />
                  <defs>
                    <linearGradient id="paint0_linear" x1="86" y1="0" x2="86" y2="172" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#3056D3" stop-opacity="0.09" />
                      <stop offset="1" stop-color="#C4C4C4" stop-opacity="0" />
                    </linearGradient>
                  </defs>
                </svg>
              </span>
              <span class="absolute right-4 top-4 z-[-1]">
                <svg width="41" height="89" viewBox="0 0 41 89" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="38.9138" cy="87.4849" r="1.42021" transform="rotate(180 38.9138 87.4849)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="74.9871" r="1.42021" transform="rotate(180 38.9138 74.9871)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="62.4892" r="1.42021" transform="rotate(180 38.9138 62.4892)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="38.3457" r="1.42021" transform="rotate(180 38.9138 38.3457)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="13.634" r="1.42021" transform="rotate(180 38.9138 13.634)" fill="#3056D3" />
                  <circle cx="38.9138" cy="50.2754" r="1.42021" transform="rotate(180 38.9138 50.2754)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="26.1319" r="1.42021" transform="rotate(180 38.9138 26.1319)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="1.42021" r="1.42021" transform="rotate(180 38.9138 1.42021)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="87.4849" r="1.42021" transform="rotate(180 26.4157 87.4849)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="74.9871" r="1.42021" transform="rotate(180 26.4157 74.9871)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="62.4892" r="1.42021" transform="rotate(180 26.4157 62.4892)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="38.3457" r="1.42021" transform="rotate(180 26.4157 38.3457)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="13.634" r="1.42021" transform="rotate(180 26.4157 13.634)" fill="#3056D3" />
                  <circle cx="26.4157" cy="50.2754" r="1.42021" transform="rotate(180 26.4157 50.2754)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="26.1319" r="1.42021" transform="rotate(180 26.4157 26.1319)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="1.4202" r="1.42021" transform="rotate(180 26.4157 1.4202)" fill="#3056D3" />
                  <circle cx="13.9177" cy="87.4849" r="1.42021" transform="rotate(180 13.9177 87.4849)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="74.9871" r="1.42021" transform="rotate(180 13.9177 74.9871)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="62.4892" r="1.42021" transform="rotate(180 13.9177 62.4892)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="38.3457" r="1.42021" transform="rotate(180 13.9177 38.3457)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="13.634" r="1.42021" transform="rotate(180 13.9177 13.634)" fill="#3056D3" />
                  <circle cx="13.9177" cy="50.2754" r="1.42021" transform="rotate(180 13.9177 50.2754)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="26.1319" r="1.42021" transform="rotate(180 13.9177 26.1319)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="1.42019" r="1.42021" transform="rotate(180 13.9177 1.42019)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="87.4849" r="1.42021" transform="rotate(180 1.41963 87.4849)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="74.9871" r="1.42021" transform="rotate(180 1.41963 74.9871)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="62.4892" r="1.42021" transform="rotate(180 1.41963 62.4892)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="38.3457" r="1.42021" transform="rotate(180 1.41963 38.3457)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="13.634" r="1.42021" transform="rotate(180 1.41963 13.634)" fill="#3056D3" />
                  <circle cx="1.41963" cy="50.2754" r="1.42021" transform="rotate(180 1.41963 50.2754)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="26.1319" r="1.42021" transform="rotate(180 1.41963 26.1319)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="1.4202" r="1.42021" transform="rotate(180 1.41963 1.4202)" fill="#3056D3" />
                </svg>
              </span>
            </div>
          </div>
        </div>
        <div class="w-full px-4 md:w-1/2 lg:w-1/3">
          <div
            class="relative z-10 mb-10 overflow-hidden rounded-[10px] border-2 border-stroke bg-white px-8 py-10 shadow-pricing sm:p-12 lg:px-6 lg:py-10 xl:p-[50px] dark:border-dark-3 dark:bg-dark-2">
            <span class="mb-3 block text-lg font-semibold text-primary">
              Business
            </span>
            <h2 class="mb-5 text-[42px] font-bold text-dark dark:text-white">
              <span>$199</span>
              <span class="text-base font-medium text-body-color dark:text-dark-6">
                / year
              </span>
            </h2>
            <p class="mb-8 border-b border-stroke pb-8 text-base text-body-color dark:border-dark-3 dark:text-dark-6">
              Perfect for using in a Business website or a client project.
            </p>
            <div class="mb-9 flex flex-col gap-[14px]">
              <p class="text-base text-body-color dark:text-dark-6">
                5 Users
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                All UI components
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Lifetime access
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Free updates
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Use on 3 (Three) project
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                4 Months support
              </p>
            </div>
            <a href="javascript:void(0)"
              class="block w-full rounded-md border border-primary bg-primary p-3 text-center text-base font-medium text-white transition hover:bg-primary/90">
              Choose Business
            </a>

            <div>
              <span class="absolute right-0 top-7 z-[-1]">
                <svg width="77" height="172" viewBox="0 0 77 172" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="86" cy="86" r="86" fill="url(#paint0_linear)" />
                  <defs>
                    <linearGradient id="paint0_linear" x1="86" y1="0" x2="86" y2="172" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#3056D3" stop-opacity="0.09" />
                      <stop offset="1" stop-color="#C4C4C4" stop-opacity="0" />
                    </linearGradient>
                  </defs>
                </svg>
              </span>
              <span class="absolute right-4 top-4 z-[-1]">
                <svg width="41" height="89" viewBox="0 0 41 89" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="38.9138" cy="87.4849" r="1.42021" transform="rotate(180 38.9138 87.4849)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="74.9871" r="1.42021" transform="rotate(180 38.9138 74.9871)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="62.4892" r="1.42021" transform="rotate(180 38.9138 62.4892)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="38.3457" r="1.42021" transform="rotate(180 38.9138 38.3457)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="13.634" r="1.42021" transform="rotate(180 38.9138 13.634)" fill="#3056D3" />
                  <circle cx="38.9138" cy="50.2754" r="1.42021" transform="rotate(180 38.9138 50.2754)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="26.1319" r="1.42021" transform="rotate(180 38.9138 26.1319)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="1.42021" r="1.42021" transform="rotate(180 38.9138 1.42021)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="87.4849" r="1.42021" transform="rotate(180 26.4157 87.4849)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="74.9871" r="1.42021" transform="rotate(180 26.4157 74.9871)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="62.4892" r="1.42021" transform="rotate(180 26.4157 62.4892)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="38.3457" r="1.42021" transform="rotate(180 26.4157 38.3457)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="13.634" r="1.42021" transform="rotate(180 26.4157 13.634)" fill="#3056D3" />
                  <circle cx="26.4157" cy="50.2754" r="1.42021" transform="rotate(180 26.4157 50.2754)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="26.1319" r="1.42021" transform="rotate(180 26.4157 26.1319)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="1.4202" r="1.42021" transform="rotate(180 26.4157 1.4202)" fill="#3056D3" />
                  <circle cx="13.9177" cy="87.4849" r="1.42021" transform="rotate(180 13.9177 87.4849)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="74.9871" r="1.42021" transform="rotate(180 13.9177 74.9871)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="62.4892" r="1.42021" transform="rotate(180 13.9177 62.4892)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="38.3457" r="1.42021" transform="rotate(180 13.9177 38.3457)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="13.634" r="1.42021" transform="rotate(180 13.9177 13.634)" fill="#3056D3" />
                  <circle cx="13.9177" cy="50.2754" r="1.42021" transform="rotate(180 13.9177 50.2754)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="26.1319" r="1.42021" transform="rotate(180 13.9177 26.1319)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="1.42019" r="1.42021" transform="rotate(180 13.9177 1.42019)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="87.4849" r="1.42021" transform="rotate(180 1.41963 87.4849)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="74.9871" r="1.42021" transform="rotate(180 1.41963 74.9871)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="62.4892" r="1.42021" transform="rotate(180 1.41963 62.4892)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="38.3457" r="1.42021" transform="rotate(180 1.41963 38.3457)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="13.634" r="1.42021" transform="rotate(180 1.41963 13.634)" fill="#3056D3" />
                  <circle cx="1.41963" cy="50.2754" r="1.42021" transform="rotate(180 1.41963 50.2754)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="26.1319" r="1.42021" transform="rotate(180 1.41963 26.1319)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="1.4202" r="1.42021" transform="rotate(180 1.41963 1.4202)" fill="#3056D3" />
                </svg>
              </span>
            </div>
          </div>
        </div>
        <div class="w-full px-4 md:w-1/2 lg:w-1/3">
          <div
            class="relative z-10 mb-10 overflow-hidden rounded-[10px] border-2 border-stroke bg-white px-8 py-10 shadow-pricing sm:p-12 lg:px-6 lg:py-10 xl:p-[50px] dark:border-dark-3 dark:bg-dark-2">
            <span class="mb-3 block text-lg font-semibold text-primary">
              Professional
            </span>
            <h2 class="mb-5 text-[42px] font-bold text-dark dark:text-white">
              <span>$256</span>
              <span class="text-base font-medium text-body-color dark:text-dark-6">
                / year
              </span>
            </h2>
            <p class="mb-8 border-b border-stroke pb-8 text-base text-body-color dark:border-dark-3 dark:text-dark-6">
              Perfect for using in a Professional website or a client project.
            </p>
            <div class="mb-9 flex flex-col gap-[14px]">
              <p class="text-base text-body-color dark:text-dark-6">
                Unlimited Users
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                All UI components
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Lifetime access
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Free updates
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                Use on Unlimited project
              </p>
              <p class="text-base text-body-color dark:text-dark-6">
                12 Months support
              </p>
            </div>
            <a href="javascript:void(0)"
              class="block w-full rounded-md border border-stroke bg-transparent p-3 text-center text-base font-medium text-primary transition hover:border-primary hover:bg-primary hover:text-white dark:border-dark-3">
              Choose Professional
            </a>

            <div>
              <span class="absolute right-0 top-7 z-[-1]">
                <svg width="77" height="172" viewBox="0 0 77 172" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="86" cy="86" r="86" fill="url(#paint0_linear)" />
                  <defs>
                    <linearGradient id="paint0_linear" x1="86" y1="0" x2="86" y2="172" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#3056D3" stop-opacity="0.09" />
                      <stop offset="1" stop-color="#C4C4C4" stop-opacity="0" />
                    </linearGradient>
                  </defs>
                </svg>
              </span>
              <span class="absolute right-4 top-4 z-[-1]">
                <svg width="41" height="89" viewBox="0 0 41 89" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="38.9138" cy="87.4849" r="1.42021" transform="rotate(180 38.9138 87.4849)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="74.9871" r="1.42021" transform="rotate(180 38.9138 74.9871)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="62.4892" r="1.42021" transform="rotate(180 38.9138 62.4892)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="38.3457" r="1.42021" transform="rotate(180 38.9138 38.3457)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="13.634" r="1.42021" transform="rotate(180 38.9138 13.634)" fill="#3056D3" />
                  <circle cx="38.9138" cy="50.2754" r="1.42021" transform="rotate(180 38.9138 50.2754)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="26.1319" r="1.42021" transform="rotate(180 38.9138 26.1319)"
                    fill="#3056D3" />
                  <circle cx="38.9138" cy="1.42021" r="1.42021" transform="rotate(180 38.9138 1.42021)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="87.4849" r="1.42021" transform="rotate(180 26.4157 87.4849)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="74.9871" r="1.42021" transform="rotate(180 26.4157 74.9871)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="62.4892" r="1.42021" transform="rotate(180 26.4157 62.4892)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="38.3457" r="1.42021" transform="rotate(180 26.4157 38.3457)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="13.634" r="1.42021" transform="rotate(180 26.4157 13.634)" fill="#3056D3" />
                  <circle cx="26.4157" cy="50.2754" r="1.42021" transform="rotate(180 26.4157 50.2754)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="26.1319" r="1.42021" transform="rotate(180 26.4157 26.1319)"
                    fill="#3056D3" />
                  <circle cx="26.4157" cy="1.4202" r="1.42021" transform="rotate(180 26.4157 1.4202)" fill="#3056D3" />
                  <circle cx="13.9177" cy="87.4849" r="1.42021" transform="rotate(180 13.9177 87.4849)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="74.9871" r="1.42021" transform="rotate(180 13.9177 74.9871)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="62.4892" r="1.42021" transform="rotate(180 13.9177 62.4892)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="38.3457" r="1.42021" transform="rotate(180 13.9177 38.3457)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="13.634" r="1.42021" transform="rotate(180 13.9177 13.634)" fill="#3056D3" />
                  <circle cx="13.9177" cy="50.2754" r="1.42021" transform="rotate(180 13.9177 50.2754)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="26.1319" r="1.42021" transform="rotate(180 13.9177 26.1319)"
                    fill="#3056D3" />
                  <circle cx="13.9177" cy="1.42019" r="1.42021" transform="rotate(180 13.9177 1.42019)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="87.4849" r="1.42021" transform="rotate(180 1.41963 87.4849)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="74.9871" r="1.42021" transform="rotate(180 1.41963 74.9871)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="62.4892" r="1.42021" transform="rotate(180 1.41963 62.4892)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="38.3457" r="1.42021" transform="rotate(180 1.41963 38.3457)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="13.634" r="1.42021" transform="rotate(180 1.41963 13.634)" fill="#3056D3" />
                  <circle cx="1.41963" cy="50.2754" r="1.42021" transform="rotate(180 1.41963 50.2754)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="26.1319" r="1.42021" transform="rotate(180 1.41963 26.1319)"
                    fill="#3056D3" />
                  <circle cx="1.41963" cy="1.4202" r="1.42021" transform="rotate(180 1.41963 1.4202)" fill="#3056D3" />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- ====== Pricing Section End -->
</body>

</html>