<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Call To Action | TailGrids</title>
  <link rel="shortcut icon" href="../../assets/images/favicon.svg" type="image/x-icon" />
  <link rel="stylesheet" href="../../assets/css/tailwind.css" />
</head>

<body>
  <!-- ====== Call To Action Section Start -->
  <section class="bg-white py-20 lg:py-[120px] dark:bg-dark">
    <div class="container mx-auto">
      <div class="relative z-10 overflow-hidden rounded-sm bg-primary px-8 py-12 md:p-[70px]">
        <div class="-mx-4 flex flex-wrap items-center">
          <div class="w-full px-4 lg:w-1/2">
            <span class="mb-4 block text-base font-medium text-white">
              Find Your Next Dream App
            </span>
            <h2 class="mb-6 text-3xl font-bold leading-tight text-white sm:mb-8 sm:text-[40px]/[48px] lg:mb-0">
              <span class="xs:block"> Get started with </span>
              <span>our free trial</span>
            </h2>
          </div>
          <div class="w-full px-4 lg:w-1/2">
            <div class="flex flex-wrap lg:justify-end">
              <a href="javascript:void(0)"
                class="hover:bg-shadow-1 my-1 mr-4 inline-flex rounded-md bg-white px-7 py-3 text-base font-medium text-primary transition">
                Get Pro Version
              </a>
              <a href="javascript:void(0)"
                class="my-1 inline-flex rounded-md bg-secondary px-7 py-3 text-base font-medium text-white transition hover:bg-secondary/90">
                Start Free Trial
              </a>
            </div>
          </div>
        </div>

        <div>
          <span class="absolute left-0 top-0 z-[-1]">
            <svg width="189" height="162" viewBox="0 0 189 162" fill="none" xmlns="http://www.w3.org/2000/svg">
              <ellipse cx="16" cy="-16.5" rx="173" ry="178.5" transform="rotate(180 16 -16.5)"
                fill="url(#paint0_linear)" />
              <defs>
                <linearGradient id="paint0_linear" x1="-157" y1="-107.754" x2="98.5011" y2="-106.425"
                  gradientUnits="userSpaceOnUse">
                  <stop stop-color="white" stop-opacity="0.07" />
                  <stop offset="1" stop-color="white" stop-opacity="0" />
                </linearGradient>
              </defs>
            </svg>
          </span>
          <span class="absolute bottom-0 right-0 z-[-1]">
            <svg width="191" height="208" viewBox="0 0 191 208" fill="none" xmlns="http://www.w3.org/2000/svg">
              <ellipse cx="173" cy="178.5" rx="173" ry="178.5" fill="url(#paint0_linear)" />
              <defs>
                <linearGradient id="paint0_linear" x1="-3.27832e-05" y1="87.2457" x2="255.501" y2="88.5747"
                  gradientUnits="userSpaceOnUse">
                  <stop stop-color="white" stop-opacity="0.07" />
                  <stop offset="1" stop-color="white" stop-opacity="0" />
                </linearGradient>
              </defs>
            </svg>
          </span>
        </div>
      </div>
    </div>
  </section>
  <!-- ====== Call To Action Section End -->
</body>

</html>