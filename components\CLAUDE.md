# Components Context - AI-Powered Personal Finance Tracker

## Component Architecture Overview
This directory contains custom React components that extend and integrate with the existing TailGrids component library. All components follow strict TypeScript patterns and maintain consistency with the TailGrids design system.

## TailGrids Integration Strategy

### Extension Pattern
Components in this directory **extend** rather than replace TailGrids components:

```typescript
// ✅ Correct approach - Extend TailGrids components
import { BaseButton } from '@/core-components/buttons/button'

export const FinanceButton = ({ variant, children, ...props }) => {
  return (
    <BaseButton 
      className={cn('finance-button', variantClasses[variant])}
      {...props}
    >
      {children}
    </BaseButton>
  )
}

// ❌ Avoid recreating components that exist in TailGrids
```

### Component Categories

#### `/ui/` - Core UI Extensions
Components that extend TailGrids core components with additional functionality:
- **Button**: Adds loading states and finance-specific variants
- **Input**: Enhanced with validation, labels, and error states
- **Card**: Extended with finance-specific layouts
- **Alert**: Additional variants for financial notifications
- **Progress**: Enhanced progress indicators for setup flows

#### `/forms/` - Form Components
Finance-specific form components built on TailGrids form elements:
- **SetupForms**: Multi-step onboarding form components
- **TransactionForms**: Transaction creation and editing
- **SettingsForms**: User preference and configuration forms

#### `/charts/` - Data Visualization
Chart components for financial data visualization:
- **DashboardCharts**: Overview charts for the main dashboard
- **AnalysisCharts**: Detailed charts for AI analysis results
- **ComparisonCharts**: Period-over-period comparisons

#### `/layout/` - Layout Components
Application layout and navigation components:
- **Header**: Main navigation with user menu
- **Sidebar**: Secondary navigation and quick actions
- **Footer**: Application footer with links and info

## Component Development Standards

### TypeScript Requirements
```typescript
// All components must be strictly typed
interface ComponentNameProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  // Define all props explicitly
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  children,
  className,
  variant = 'default',
  ...props
}) => {
  // Implementation
}

ComponentName.displayName = 'ComponentName'
```

### Styling Guidelines
1. **Use TailGrids classes first**: Leverage existing TailGrids utilities
2. **Extend with custom CSS**: Add custom classes only when necessary
3. **Maintain consistency**: Follow established color and spacing patterns
4. **Support dark mode**: Ensure all components work in dark mode

### Accessibility Standards
- Use semantic HTML elements
- Implement proper ARIA attributes
- Ensure keyboard navigation works
- Maintain color contrast ratios
- Support screen readers

### Performance Considerations
- Use React.memo() for expensive components
- Implement lazy loading for large components
- Optimize bundle size with tree shaking
- Avoid unnecessary re-renders

## Integration with TailGrids Components

### Direct Usage
```typescript
// Use TailGrids components directly when they meet requirements
import { Table } from '@/application/tables/table-01'
import { Modal } from '@/core-components/modals/modal-07'

// These components can be used as-is with TailGrids styling
```

### Extension Pattern
```typescript
// Extend TailGrids components when additional functionality is needed
import { BaseCard } from '@/application/cards/card-01'

export const FinanceCard = ({ title, value, trend, ...props }) => {
  return (
    <BaseCard className="finance-card" {...props}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <TrendIndicator trend={trend} />
      </CardContent>
    </BaseCard>
  )
}
```

### Composition Pattern
```typescript
// Compose multiple TailGrids components for complex functionality
export const TransactionRow = ({ transaction }) => {
  return (
    <div className="flex items-center justify-between p-4">
      <Avatar src={transaction.category.icon} />
      <div className="flex-1">
        <h4>{transaction.description}</h4>
        <Badge variant={transaction.type}>{transaction.category.name}</Badge>
      </div>
      <span className="font-semibold">{formatCurrency(transaction.amount)}</span>
    </div>
  )
}
```

## Testing Standards

### Component Testing
```typescript
// Use React Testing Library for component tests
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { FinanceButton } from './finance-button'

describe('FinanceButton', () => {
  it('renders with correct variant styling', () => {
    render(<FinanceButton variant="success">Save</FinanceButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-green-600')
  })

  it('shows loading state correctly', () => {
    render(<FinanceButton loading>Save</FinanceButton>)
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })
})
```

### Integration Testing
- Test component integration with TailGrids components
- Verify responsive behavior across screen sizes
- Test accessibility features
- Validate form submission workflows

## Documentation Requirements

### Component Documentation
Each component should include:
```typescript
/**
 * FinanceButton extends the base TailGrids button with loading states
 * and finance-specific styling variants.
 * 
 * @example
 * <FinanceButton variant="success" loading={isSubmitting}>
 *   Save Transaction
 * </FinanceButton>
 */
```

### Usage Examples
Provide clear usage examples for each component:
- Basic usage
- All available props
- Common patterns
- Integration with forms

## Error Boundaries and Fallbacks

### Component-Level Error Boundaries
```typescript
export const ComponentErrorBoundary = ({ children, fallback }) => {
  return (
    <ErrorBoundary
      FallbackComponent={fallback || DefaultErrorFallback}
      onError={(error) => console.error('Component error:', error)}
    >
      {children}
    </ErrorBoundary>
  )
}
```

### Loading States
All components should handle loading states gracefully:
- Skeleton loaders for content
- Spinner indicators for actions
- Progressive loading for charts

## Best Practices

### Component Organization
1. **Single Responsibility**: Each component should have one clear purpose
2. **Composition over Inheritance**: Prefer composing smaller components
3. **Prop Drilling Avoidance**: Use context for deeply nested props
4. **Reusability**: Design components for reuse across features

### State Management
- Use local state for component-specific state
- Use React Query for server state
- Use Context for shared UI state
- Avoid prop drilling with proper component composition

### Performance Optimization
- Implement proper memoization
- Use callback memoization for event handlers
- Optimize bundle size with proper imports
- Monitor component re-render performance

This component architecture ensures seamless integration with TailGrids while providing the enhanced functionality needed for the AI-powered personal finance tracker.
