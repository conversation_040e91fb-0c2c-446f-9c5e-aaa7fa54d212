{"name": "ai-finance-tracker", "version": "1.0.0", "main": "index.js", "license": "MIT", "keywords": ["component-library", "ui-components", "tailwind", "react-components", "vue-components", "tailwindcss", "tailwind-css", "alpinej<PERSON>", "tailwindui", "tailwind-ui", "tailwind-blocks", "css", "html", "template", "components", "ui", "landing-page", "blocks", "free", "tailgrids", "tailwind-ui", "v<PERSON><PERSON><PERSON>", "reactjs", "frontend", "web-development", "responsive-design", "design-system", "ui-kit", "vue-ui", "react-ui", "javascript", "frontend-components", "design-library", "ui-library", "web-components", "headless-ui", "vue", "react", "single-page-applications", "spa", "high-performance", "reusable-components", "vue-templates", "react-templates", "tailwind-css-sections", "tailgrids", "flowbite", "daisyui"], "author": "AI Finance Tracker Team", "homepage": "https://tailgrids.com/", "description": "AI-powered personal finance tracker built with Next.js, TailGrids, Supabase, and AI services", "bugs": "https://github.com/TailGrids/tailwind-ui-components/issues", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "build-css": "npx @tailwindcss/cli -i ./src/tailwind.css -o ./assets/css/tailwind.css -w", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "supabase:gen-types": "supabase gen types typescript --local > types/database.ts", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset"}, "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@supabase/supabase-js": "^2.39.0", "@supabase/auth-helpers-nextjs": "^0.9.0", "@supabase/auth-helpers-react": "^0.4.2", "@google/generative-ai": "^0.2.1", "@mistralai/mistralai": "^0.1.3", "@tanstack/react-query": "^5.17.0", "@hookform/resolvers": "^3.3.4", "react-hook-form": "^7.49.3", "zod": "^3.22.4", "date-fns": "^3.2.0", "recharts": "^2.10.3", "lucide-react": "^0.321.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.1", "uuid": "^9.0.1", "decimal.js": "^10.4.3", "framer-motion": "^11.0.3"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@tailwindcss/postcss": "^4.0.9", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.11", "tailgrids": "^2.2.5", "tailwindcss": "^4.0.9", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.2.0", "jest-environment-jsdom": "^29.7.0", "supabase": "^1.142.2"}, "repository": {"type": "git", "url": "https://github.com/ai-finance-tracker/ai-finance-tracker"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}