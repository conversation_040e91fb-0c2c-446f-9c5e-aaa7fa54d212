<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Button groups | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../../assets/css/tailwind.css" />
  </head>
  <body>
    <!-- ====== Button Group Section Start -->
    <section class="pb-10 pt-20 lg:pb-20 lg:pt-[120px] dark:bg-dark">
      <div class="container">
        <div class="flex justify-center">
          <div
            class="inline-flex items-center overflow-hidden rounded-lg border border-stroke dark:border-dark-3"
          >
            <button
              class="border-r border-stroke px-4 py-3 text-base font-medium text-dark last-of-type:border-r-0 hover:bg-gray-2 hover:text-primary dark:border-dark-3 dark:text-white"
            >
              Button Text
            </button>
            <button
              class="border-r border-stroke px-4 py-3 text-base font-medium text-dark last-of-type:border-r-0 hover:bg-gray-2 hover:text-primary dark:border-dark-3 dark:text-white"
            >
              Button Text
            </button>
            <button
              class="border-r border-stroke px-4 py-3 text-base font-medium text-dark last-of-type:border-r-0 hover:bg-gray-2 hover:text-primary dark:border-dark-3 dark:text-white"
            >
              Button Text
            </button>
          </div>
        </div>
      </div>
    </section>
    <!-- ====== Buttons Group Section End -->
  </body>
</html>
