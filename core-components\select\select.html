<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Select | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../../assets/css/tailwind.css" />
  </head>
  <body>
    <!-- ====== Select Start -->
    <section class="bg-white py-20 dark:bg-dark">
      <div class="container">
        <div class="w-full max-w-[500px] space-y-10">
          <!-- Select Style One -->
          <div>
            <label
              for="default-select"
              class="mb-2.5 block text-base font-medium text-dark dark:text-white"
            >
              Message
            </label>
            <div class="relative">
              <select
                name="default-select"
                class="w-full appearance-none rounded-lg border border-stroke bg-transparent py-3 pl-5 pr-12 text-dark outline-hidden focus:border-primary dark:border-dark-3 dark:text-white"
              >
                <option value="Select option 1">Select option 1</option>
                <option value="Select option 2">Select option 2</option>
                <option value="Select option 3">Select option 3</option>
                <option value="Select option 4">Select option 4</option>
              </select>
              <span
                class="pointer-events-none absolute right-0 top-0 flex h-full w-12 items-center justify-center text-dark-5"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2.29645 5.15354L2.29642 5.15357L2.30065 5.1577L7.65065 10.3827L8.00167 10.7255L8.35105 10.381L13.7011 5.10603L13.7011 5.10604L13.7036 5.10354C13.7221 5.08499 13.7386 5.08124 13.75 5.08124C13.7614 5.08124 13.7779 5.08499 13.7964 5.10354C13.815 5.12209 13.8188 5.13859 13.8188 5.14999C13.8188 5.1612 13.8151 5.17734 13.7974 5.19552L8.04956 10.8433L8.04955 10.8433L8.04645 10.8464C8.01604 10.8768 7.99596 10.8921 7.98519 10.8992C7.97756 10.8983 7.97267 10.8968 7.96862 10.8952C7.96236 10.8929 7.94954 10.887 7.92882 10.8721L2.20263 5.2455C2.18488 5.22733 2.18125 5.2112 2.18125 5.19999C2.18125 5.18859 2.18501 5.17209 2.20355 5.15354C2.2221 5.13499 2.2386 5.13124 2.25 5.13124C2.2614 5.13124 2.2779 5.13499 2.29645 5.15354Z"
                    fill="currentColor"
                    stroke="currentColor"
                  />
                </svg>
              </span>
            </div>
          </div>

          <!-- Select Style Two -->
          <div>
            <label
              for="choose-country"
              class="mb-2.5 block text-base font-medium text-dark dark:text-white"
            >
              Choose Country
            </label>
            <div class="relative">
              <select
                name="choose-country"
                class="w-full appearance-none rounded-lg border border-stroke bg-transparent px-12 py-3 text-dark outline-hidden focus:border-primary dark:border-dark-3 dark:text-white"
              >
                <option value="USA">USA</option>
                <option value="UK">UK</option>
                <option value="Canada">Canada</option>
                <option value="BD">BD</option>
              </select>
              <span
                class="pointer-events-none absolute left-0 top-0 flex h-full w-12 items-center justify-center text-dark-5"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_2470_1484)">
                    <path
                      d="M18.1562 5.1875C16.4687 2.3125 13.4687 0.59375 10.1562 0.53125C10.0625 0.53125 9.9375 0.53125 9.84375 0.53125C6.53125 0.59375 3.53125 2.34375 1.84375 5.1875C1 6.625 0.53125 8.3125 0.53125 10C0.53125 12.0312 1.15625 13.9375 2.34375 15.5625C4.125 17.9687 6.84375 19.4062 9.8125 19.4375C9.875 19.4375 9.90625 19.4375 9.96875 19.4375C10.0312 19.4375 10.0625 19.4375 10.125 19.4375C13.125 19.4062 15.8437 17.9687 17.5937 15.5625C18.7812 13.9375 19.4062 12 19.4062 10C19.4687 8.3125 19 6.65625 18.1562 5.1875ZM16.4375 5.1875C15.2812 5.5625 14.0625 5.84375 12.8437 6C12.5 4.625 11.9687 3.3125 11.2812 2.0625C13.375 2.40625 15.1875 3.5 16.4375 5.1875ZM9.8125 17.75C9.25 16.7812 8.78125 15.7812 8.4375 14.7812C9.34375 14.7187 10.2812 14.7187 11.1875 14.75C10.8437 15.7812 10.375 16.7812 9.8125 17.75ZM11.4375 6.125C10.3437 6.1875 9.21875 6.1875 8.15625 6.09375C8.53125 4.75 9.0625 3.46875 9.78125 2.25C10.5312 3.5 11.0937 4.78125 11.4375 6.125ZM7.84375 7.5C8.53125 7.5625 9.25 7.59375 10 7.59375C10.5937 7.59375 11.1875 7.5625 11.75 7.53125C11.875 8.34375 11.9687 9.1875 11.9687 10C11.9687 11.125 11.8437 12.25 11.5937 13.3438C10.4375 13.25 9.21875 13.2812 8.0625 13.375C7.8125 12.2812 7.65625 11.1562 7.65625 10C7.625 9.15625 7.71875 8.3125 7.84375 7.5ZM8.25 2.15625C7.59375 3.375 7.09375 4.65625 6.75 5.96875C5.65625 5.8125 4.59375 5.5625 3.5625 5.21875C4.71875 3.625 6.375 2.5625 8.25 2.15625ZM1.96875 10C1.96875 8.75 2.25 7.53125 2.8125 6.40625C4 6.8125 5.21875 7.125 6.46875 7.3125C6.3125 8.1875 6.25 9.09375 6.25 10C6.25 11.1875 6.375 12.375 6.65625 13.5312C5.5 13.7187 4.375 13.9687 3.25 14.3125C2.40625 13.0312 1.96875 11.5625 1.96875 10ZM4.15625 15.5312C5.09375 15.25 6.03125 15.0625 7 14.9062C7.3125 15.9063 7.75 16.9062 8.25 17.8437C6.6875 17.5 5.25 16.7187 4.15625 15.5312ZM11.3125 17.9375C11.8437 16.9375 12.3125 15.9062 12.625 14.875C13.7187 15 14.8125 15.2188 15.8437 15.5312C14.625 16.8125 13.0625 17.6563 11.3125 17.9375ZM16.7812 14.3438C15.5625 13.9375 14.2812 13.6875 13 13.5C13.25 12.375 13.375 11.1875 13.375 10C13.375 9.125 13.3125 8.25 13.1562 7.375C14.5312 7.1875 15.9062 6.875 17.2187 6.40625C17.7812 7.5 18.0625 8.75 18.0625 10C18.0625 11.5625 17.5937 13.0313 16.7812 14.3438Z"
                      fill="currentColor"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_2470_1484">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <span
                class="pointer-events-none absolute right-0 top-0 flex h-full w-12 items-center justify-center text-dark-5"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2.29645 5.15354L2.29642 5.15357L2.30065 5.1577L7.65065 10.3827L8.00167 10.7255L8.35105 10.381L13.7011 5.10603L13.7011 5.10604L13.7036 5.10354C13.7221 5.08499 13.7386 5.08124 13.75 5.08124C13.7614 5.08124 13.7779 5.08499 13.7964 5.10354C13.815 5.12209 13.8188 5.13859 13.8188 5.14999C13.8188 5.1612 13.8151 5.17734 13.7974 5.19552L8.04956 10.8433L8.04955 10.8433L8.04645 10.8464C8.01604 10.8768 7.99596 10.8921 7.98519 10.8992C7.97756 10.8983 7.97267 10.8968 7.96862 10.8952C7.96236 10.8929 7.94954 10.887 7.92882 10.8721L2.20263 5.2455C2.18488 5.22733 2.18125 5.2112 2.18125 5.19999C2.18125 5.18859 2.18501 5.17209 2.20355 5.15354C2.2221 5.13499 2.2386 5.13124 2.25 5.13124C2.2614 5.13124 2.2779 5.13499 2.29645 5.15354Z"
                    fill="currentColor"
                    stroke="currentColor"
                  />
                </svg>
              </span>
            </div>
          </div>

          <!-- Select Style Three -->
          <div>
            <label
              for="multiselect-dropdown"
              class="mb-2.5 block text-base font-medium text-dark dark:text-white"
            >
              Multiselect Dropdown
            </label>
            <div
              class="relative flex flex-wrap items-center gap-2 rounded-lg border border-stroke bg-transparent px-4 py-3 dark:border-dark-3"
            >
              <span
                class="inline-flex items-center gap-2 rounded-full border border-stroke py-1 pl-3 pr-2 hover:bg-gray-2 dark:border-dark-3 dark:text-white dark:hover:bg-white/5"
              >
                Design
                <span
                  class="cursor-pointer text-dark-5 hover:text-dark dark:hover:text-white"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_2469_15115)">
                      <path
                        d="M8.00001 0.450012C3.82501 0.450012 0.450012 3.82501 0.450012 8.00001C0.450012 12.175 3.82501 15.575 8.00001 15.575C12.175 15.575 15.575 12.175 15.575 8.00001C15.575 3.82501 12.175 0.450012 8.00001 0.450012ZM8.00001 14.45C4.45001 14.45 1.57501 11.55 1.57501 8.00001C1.57501 4.45001 4.45001 1.57501 8.00001 1.57501C11.55 1.57501 14.45 4.47501 14.45 8.02501C14.45 11.55 11.55 14.45 8.00001 14.45Z"
                        fill="currentColor"
                      />
                      <path
                        d="M10.3 5.67501C10.075 5.45001 9.72501 5.45001 9.50001 5.67501L8.00001 7.20001L6.47501 5.67501C6.25001 5.45001 5.90001 5.45001 5.67501 5.67501C5.45001 5.90001 5.45001 6.25001 5.67501 6.47501L7.20001 8.00001L5.67501 9.52501C5.45001 9.75001 5.45001 10.1 5.67501 10.325C5.77501 10.425 5.92501 10.5 6.07501 10.5C6.22501 10.5 6.37501 10.45 6.47501 10.325L8.00001 8.80001L9.52501 10.325C9.62501 10.425 9.77501 10.5 9.92501 10.5C10.075 10.5 10.225 10.45 10.325 10.325C10.55 10.1 10.55 9.75001 10.325 9.52501L8.80001 8.00001L10.325 6.47501C10.525 6.25001 10.525 5.90001 10.3 5.67501Z"
                        fill="currentColor"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_2469_15115">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
              </span>

              <span
                class="inline-flex items-center gap-2 rounded-full border border-stroke py-1 pl-3 pr-2 hover:bg-gray-2 dark:border-dark-3 dark:text-white dark:hover:bg-white/5"
              >
                Development
                <span
                  class="cursor-pointer text-dark-5 hover:text-dark dark:hover:text-white"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_2469_15115)">
                      <path
                        d="M8.00001 0.450012C3.82501 0.450012 0.450012 3.82501 0.450012 8.00001C0.450012 12.175 3.82501 15.575 8.00001 15.575C12.175 15.575 15.575 12.175 15.575 8.00001C15.575 3.82501 12.175 0.450012 8.00001 0.450012ZM8.00001 14.45C4.45001 14.45 1.57501 11.55 1.57501 8.00001C1.57501 4.45001 4.45001 1.57501 8.00001 1.57501C11.55 1.57501 14.45 4.47501 14.45 8.02501C14.45 11.55 11.55 14.45 8.00001 14.45Z"
                        fill="currentColor"
                      />
                      <path
                        d="M10.3 5.67501C10.075 5.45001 9.72501 5.45001 9.50001 5.67501L8.00001 7.20001L6.47501 5.67501C6.25001 5.45001 5.90001 5.45001 5.67501 5.67501C5.45001 5.90001 5.45001 6.25001 5.67501 6.47501L7.20001 8.00001L5.67501 9.52501C5.45001 9.75001 5.45001 10.1 5.67501 10.325C5.77501 10.425 5.92501 10.5 6.07501 10.5C6.22501 10.5 6.37501 10.45 6.47501 10.325L8.00001 8.80001L9.52501 10.325C9.62501 10.425 9.77501 10.5 9.92501 10.5C10.075 10.5 10.225 10.45 10.325 10.325C10.55 10.1 10.55 9.75001 10.325 9.52501L8.80001 8.00001L10.325 6.47501C10.525 6.25001 10.525 5.90001 10.3 5.67501Z"
                        fill="currentColor"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_2469_15115">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
              </span>

              <div class="relative flex-1">
                <select
                  name="multiselect-dropdown"
                  class="appearance-none bg-transparent px-4 text-dark outline-hidden dark:text-white"
                >
                  <option value="USA">USA</option>
                  <option value="UK">UK</option>
                  <option value="Canada">Canada</option>
                  <option value="BD">BD</option>
                </select>

                <span
                  class="pointer-events-none absolute right-0 top-0 flex h-full items-center justify-center text-dark-5"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M2.29645 5.15354L2.29642 5.15357L2.30065 5.1577L7.65065 10.3827L8.00167 10.7255L8.35105 10.381L13.7011 5.10603L13.7011 5.10604L13.7036 5.10354C13.7221 5.08499 13.7386 5.08124 13.75 5.08124C13.7614 5.08124 13.7779 5.08499 13.7964 5.10354C13.815 5.12209 13.8188 5.13859 13.8188 5.14999C13.8188 5.1612 13.8151 5.17734 13.7974 5.19552L8.04956 10.8433L8.04955 10.8433L8.04645 10.8464C8.01604 10.8768 7.99596 10.8921 7.98519 10.8992C7.97756 10.8983 7.97267 10.8968 7.96862 10.8952C7.96236 10.8929 7.94954 10.887 7.92882 10.8721L2.20263 5.2455C2.18488 5.22733 2.18125 5.2112 2.18125 5.19999C2.18125 5.18859 2.18501 5.17209 2.20355 5.15354C2.2221 5.13499 2.2386 5.13124 2.25 5.13124C2.2614 5.13124 2.2779 5.13499 2.29645 5.15354Z"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                  </svg>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- ====== Select End -->
  </body>
</html>
