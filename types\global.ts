// Global type definitions for the AI Finance Tracker

export interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  avatarUrl?: string
  currency: string
  createdAt: string
  updatedAt: string
}

export interface Account {
  id: string
  userId: string
  name: string
  type: 'checking' | 'savings' | 'credit' | 'investment'
  balance: number
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  userId: string
  name: string
  type: 'income' | 'expense'
  color: string
  createdAt: string
}

export interface Transaction {
  id: string
  userId: string
  accountId: string
  categoryId?: string
  amount: number
  description: string
  date: string
  type: 'income' | 'expense' | 'transfer'
  createdAt: string
  updatedAt: string
  
  // Relations
  account?: Account
  category?: Category
}

export interface RecurringPayment {
  id: string
  userId: string
  name: string
  amount: number
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  nextDueDate: string
  categoryId?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  
  // Relations
  category?: Category
}

export interface LoanDebit {
  id: string
  userId: string
  name: string
  totalAmount: number
  remainingAmount: number
  interestRate: number
  monthlyPayment: number
  dueDate: string
  type: 'loan' | 'debt'
  createdAt: string
  updatedAt: string
}

export interface FinancialInsight {
  id: string
  userId: string
  type: 'spending_pattern' | 'budget_recommendation' | 'saving_opportunity' | 'risk_assessment'
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  actionable: boolean
  createdAt: string
}

export interface APIConfiguration {
  id: string
  userId: string
  supabaseUrl: string
  supabaseAnonKey: string
  geminiApiKey?: string
  mistralApiKey?: string
  isConfigured: boolean
  createdAt: string
  updatedAt: string
}

// Form types
export interface SetupFormData {
  // API Configuration
  supabaseUrl: string
  supabaseAnonKey: string
  geminiApiKey?: string
  mistralApiKey?: string
  
  // Profile Setup
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  currency: string
  agreeToTerms: boolean
  
  // Preferences
  aiResponseDetail: 'minimal' | 'detailed' | 'comprehensive'
  aiPersonality: 'professional' | 'friendly' | 'casual'
  aiLearning: boolean
}

export interface TransactionFormData {
  accountId: string
  categoryId?: string
  amount: number
  description: string
  date: string
  type: 'income' | 'expense' | 'transfer'
}

// API Response types
export interface APIResponse<T = any> {
  data?: T
  error?: string
  message?: string
  success: boolean
}

// Loading and error states
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Chart data types
export interface ChartData {
  label: string
  value: number
  color?: string
}

export interface TimeSeriesData {
  date: string
  amount: number
  category?: string
}

// AI Analysis types
export interface AnalysisRequest {
  userId: string
  timeframe: '1_month' | '3_months' | '6_months' | '1_year'
  analysisType: 'spending' | 'income' | 'savings' | 'comprehensive'
  includeRecommendations: boolean
}

export interface AnalysisResult {
  insights: FinancialInsight[]
  recommendations: string[]
  riskScore: number
  confidenceLevel: number
  generatedAt: string
}

// Navigation types
export interface NavItem {
  title: string
  href: string
  icon?: React.ComponentType
  description?: string
  disabled?: boolean
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredField<T, K extends keyof T> = T & Required<Pick<T, K>>
