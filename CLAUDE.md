# AI-Powered Personal Finance Tracker - Master Context

## Project Overview
This is an AI-powered personal finance tracking application built using TailGrids UI components, Next.js, and various AI services. The application helps users manage their finances through intelligent automation and insights.

## Architecture Stack
- **Frontend**: Next.js 14+ with TypeScript
- **UI Components**: TailGrids (existing library integrated)
- **Styling**: Tailwind CSS v4
- **Database**: Supabase (PostgreSQL)
- **AI Services**: 
  - Google Gemini API
  - MistralAI API
  - Claude Code Development Kit
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage

## Coding Standards

### File Organization
```
├── app/                          # Next.js 14 app directory
│   ├── (auth)/                   # Authentication routes group
│   ├── (dashboard)/              # Main app routes group
│   ├── setup/                    # Onboarding flow
│   └── globals.css               # Global styles
├── components/                   # Custom React components
│   ├── ui/                       # TailGrids integration layer
│   ├── forms/                    # Form components
│   └── charts/                   # Chart components
├── lib/                          # Utility functions
│   ├── supabase/                 # Supabase client & types
│   ├── ai/                       # AI service integrations
│   └── utils.ts                  # General utilities
├── types/                        # TypeScript definitions
└── public/                       # Static assets
```

### Component Standards
1. **Use TailGrids Components**: Always leverage existing TailGrids components from `core-components/` and `application/` directories
2. **TypeScript**: All components must be strictly typed
3. **Responsive Design**: All UI must be mobile-first responsive
4. **Accessibility**: Follow WCAG 2.1 AA standards
5. **Performance**: Use Next.js 14+ features (Server Components, Streaming)

### TailGrids Integration Rules
- Import TailGrids styles via `@plugin 'tailgrids/plugin'` in CSS
- Use existing components from:
  - `core-components/` for basic UI elements
  - `application/` for complex patterns
  - `marketing/` for landing pages (if needed)
- Never recreate components that already exist in TailGrids
- Extend TailGrids components with additional props when needed

### Database Schema Standards
```sql
-- Core tables for finance tracker
-- Users (handled by Supabase Auth)
-- Transactions
-- Categories  
-- Accounts
-- Recurring_payments
-- Loans_debits
-- AI_analysis_results
-- User_preferences
```

### API Integration Standards
- Use Supabase client for database operations
- Implement proper error handling with try/catch
- Use React Query for API state management
- Implement proper loading states
- Handle rate limiting for AI APIs

### Security Requirements
- All API keys stored in environment variables
- Implement RLS (Row Level Security) in Supabase
- Validate all inputs on both client and server
- Use HTTPS only
- Implement proper session management

## Development Workflow
1. **Planning**: Create GitHub issues for features
2. **Documentation**: Update relevant CLAUDE.md files
3. **Implementation**: Follow TDD when possible
4. **Testing**: Unit tests for utilities, integration tests for flows
5. **Review**: Use Claude Code for automated reviews
6. **Deployment**: Use Vercel for frontend, Supabase for backend

## AI Services Integration

### Google Gemini API
- Used for: Complex financial analysis and insights
- Rate limits: Respect API quotas
- Error handling: Graceful fallbacks

### MistralAI API  
- Used for: Natural language processing of transactions
- Implementation: Server-side only for security

### Claude Code Integration
- Commands available in `.claude/commands/`
- Documentation auto-loads based on task complexity
- MCP servers configured for external AI consultation

## Key Commands Available
- `/full-context` - Complete project analysis
- `/code-review` - Automated code review
- `/update-docs` - Documentation maintenance
- `/test-suite` - Run comprehensive tests
- `/deploy-check` - Pre-deployment validation

## File Import Patterns
Always import files using these patterns:
```typescript
// React imports
import React from 'react'
import { useState, useEffect } from 'react'

// Next.js imports  
import Link from 'next/link'
import Image from 'next/image'
import { redirect } from 'next/navigation'

// Third-party imports
import { createClient } from '@supabase/supabase-js'

// Internal imports
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import type { Database } from '@/types/database'
```

## Environment Variables Required
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# AI Services
GOOGLE_GEMINI_API_KEY=
MISTRAL_AI_API_KEY=

# Application
NEXTAUTH_SECRET=
NEXTAUTH_URL=
```

## Critical Integration Points
1. **TailGrids Plugin**: Ensure `@plugin 'tailgrids/plugin'` is in CSS
2. **Supabase Types**: Generate types with `supabase gen types typescript`
3. **AI Rate Limiting**: Implement proper queuing for AI requests
4. **Error Boundaries**: Wrap all major sections with error boundaries
5. **Loading States**: Use TailGrids spinners and skeletons

## Performance Targets
- First Contentful Paint: < 1.5s
- Largest Contentful Paint: < 2.5s
- Cumulative Layout Shift: < 0.1
- Time to Interactive: < 3.5s

## Deployment Checklist
- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] AI API keys validated
- [ ] TailGrids CSS properly compiled
- [ ] All tests passing
- [ ] Security headers configured
- [ ] Analytics implemented

---

This master context ensures consistent development practices across the entire AI-powered personal finance tracker project while leveraging the existing TailGrids component library.
