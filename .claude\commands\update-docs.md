# Update Documentation

Please update all project documentation to reflect current implementation and maintain accuracy:

## Master Context Update
- Review and update `/CLAUDE.md` with current project state
- Ensure coding standards reflect actual implementation
- Update architecture information with any changes
- Verify environment variable requirements are current

## Tier 1 Documentation Updates
- Update `/docs/ai-context/project-structure.md` with current file tree
- Refresh technology stack information if changed
- Update integration patterns with new implementations
- Verify deployment infrastructure matches current setup

## Tier 2 Component Documentation
- Update component-level `CLAUDE.md` files with current patterns
- Document new TailGrids component usage
- Update utility function documentation
- Refresh hook documentation with current implementations

## Tier 3 Feature Documentation  
- Update feature-specific `CLAUDE.md` files in app directories
- Document implemented vs planned features
- Update API endpoint documentation
- Refresh database schema documentation

## Integration Documentation
- Update TailGrids integration patterns
- Refresh Supabase client usage documentation
- Update AI service integration examples
- Document new authentication patterns

## Technical Documentation Updates
- Update TypeScript type definitions documentation
- Refresh API route documentation
- Update database migration documentation
- Document new environment configuration

## Process Documentation
- Update deployment checklist with current practices
- Refresh testing strategies and coverage reports
- Update security implementation documentation
- Document new development workflows

## Issue and Spec Updates
- Review and update open issues documentation
- Refresh feature specifications with implementation details
- Update user story documentation
- Document completed feature implementations

## Documentation Quality Check
- Ensure all code examples are current and functional
- Verify all links and references work correctly
- Check for outdated information or broken patterns
- Validate documentation consistency across files

## Documentation Maintenance
- Remove obsolete documentation and examples
- Consolidate duplicate information
- Improve documentation organization and navigation
- Add missing documentation for new features

## Standards Updates
- Update coding standards based on current practices
- Refresh component naming conventions
- Update file organization guidelines
- Document new architectural decisions

Please ensure all documentation accurately reflects the current state of the project and provides clear guidance for continued development.
