# Project Structure - AI-Powered Personal Finance Tracker

## Technology Stack
- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4 + TailGrids Plugin
- **UI Components**: TailGrids Library (600+ components)
- **Database**: Supabase (PostgreSQL + Auth + Storage)
- **AI Services**: Google Gemini, MistralAI
- **Development**: Claude Code Development Kit
- **Deployment**: Vercel (Frontend) + Supabase (Backend)

## Complete File Tree

```
tailwind-ui-components-main/
├── .claude/
│   └── commands/                     # Claude Code orchestration templates
│       ├── full-context.md          # Complete project analysis
│       ├── code-review.md            # Automated code review
│       ├── update-docs.md            # Documentation maintenance
│       ├── test-suite.md             # Testing workflows
│       ├── deploy-check.md           # Pre-deployment validation
│       ├── setup-api.md              # API configuration helper
│       ├── create-component.md       # TailGrids component creation
│       └── ai-analysis.md            # AI-powered insights
├── docs/
│   ├── ai-context/                   # Tier 1 - Foundation Documentation
│   │   ├── project-structure.md     # This file - complete stack info
│   │   ├── docs-overview.md          # Documentation routing map
│   │   ├── system-integration.md     # Cross-component patterns
│   │   ├── deployment-infrastructure.md # Infrastructure context
│   │   └── handoff.md                # Session continuity guide
│   ├── open-issues/                  # Issue tracking templates
│   │   ├── setup-issues.md           # Setup and configuration issues
│   │   ├── ui-issues.md              # UI/UX related issues
│   │   └── integration-issues.md     # AI/API integration issues
│   ├── specs/                        # Feature specifications
│   │   ├── setup-flow-spec.md        # Onboarding flow specification
│   │   ├── dashboard-spec.md         # Dashboard requirements
│   │   ├── ai-chat-spec.md           # AI chat interface spec
│   │   └── transaction-spec.md       # Transaction management spec
│   └── README.md                     # Documentation system guide
├── app/                              # Next.js 14 App Router
│   ├── globals.css                   # Global styles + TailGrids plugin
│   ├── layout.tsx                    # Root layout
│   ├── page.tsx                      # Dashboard (root route)
│   ├── loading.tsx                   # Global loading UI
│   ├── error.tsx                     # Global error UI
│   ├── not-found.tsx                 # 404 page
│   ├── setup/                        # Onboarding flow
│   │   ├── layout.tsx                # Setup layout
│   │   ├── api-config/
│   │   │   └── page.tsx              # API configuration screen
│   │   ├── profile-setup/
│   │   │   └── page.tsx              # Profile setup screen
│   │   └── setup-complete/
│   │       └── page.tsx              # Setup completion screen
│   ├── ai-chat/
│   │   ├── page.tsx                  # AI chat interface
│   │   └── CLAUDE.md                 # Tier 3 - AI Chat context
│   ├── transactions/
│   │   ├── page.tsx                  # Transaction management
│   │   ├── loading.tsx               # Transaction loading state
│   │   └── CLAUDE.md                 # Tier 3 - Transaction context
│   ├── loans-debits/
│   │   ├── page.tsx                  # Loans & debits tracker
│   │   └── CLAUDE.md                 # Tier 3 - Loans context
│   ├── recurring-payments/
│   │   ├── page.tsx                  # Recurring payments management
│   │   └── CLAUDE.md                 # Tier 3 - Payments context
│   ├── ai-analysis/
│   │   ├── page.tsx                  # AI-powered analysis
│   │   ├── loading.tsx               # Analysis loading state
│   │   └── CLAUDE.md                 # Tier 3 - Analysis context
│   ├── settings/
│   │   ├── page.tsx                  # Settings & configuration
│   │   ├── loading.tsx               # Settings loading state
│   │   └── CLAUDE.md                 # Tier 3 - Settings context
│   └── api/                          # API routes
│       ├── auth/                     # Authentication endpoints
│       ├── transactions/             # Transaction CRUD
│       ├── ai/                       # AI service integrations
│       │   ├── gemini/              # Google Gemini endpoints
│       │   └── mistral/             # MistralAI endpoints
│       └── setup/                    # Setup validation endpoints
├── components/                       # Custom React components
│   ├── ui/                          # TailGrids integration layer
│   │   ├── button.tsx               # Extended TailGrids button
│   │   ├── input.tsx                # Extended TailGrids input
│   │   ├── card.tsx                 # Extended TailGrids card
│   │   ├── modal.tsx                # Extended TailGrids modal
│   │   └── index.ts                 # UI component exports
│   ├── forms/                       # Form components
│   │   ├── setup-forms/             # Onboarding forms
│   │   ├── transaction-forms/       # Transaction forms
│   │   └── settings-forms/          # Settings forms
│   ├── charts/                      # Chart components
│   │   ├── dashboard-charts/        # Dashboard visualizations
│   │   └── analysis-charts/         # AI analysis charts
│   ├── layout/                      # Layout components
│   │   ├── header.tsx               # Main navigation
│   │   ├── sidebar.tsx              # Sidebar navigation
│   │   └── footer.tsx               # Footer component
│   └── CLAUDE.md                    # Tier 2 - Components context
├── lib/                             # Utility functions
│   ├── supabase/                    # Supabase client & utilities
│   │   ├── client.ts                # Supabase client setup
│   │   ├── auth.ts                  # Authentication helpers
│   │   ├── database.ts              # Database operations
│   │   └── storage.ts               # File storage helpers
│   ├── ai/                          # AI service integrations
│   │   ├── gemini.ts                # Google Gemini integration
│   │   ├── mistral.ts               # MistralAI integration
│   │   └── analysis.ts              # AI analysis utilities
│   ├── utils.ts                     # General utility functions
│   ├── validations.ts               # Input validation schemas
│   └── CLAUDE.md                    # Tier 2 - Utilities context
├── types/                           # TypeScript definitions
│   ├── database.ts                  # Supabase generated types
│   ├── auth.ts                      # Authentication types
│   ├── transactions.ts              # Transaction types
│   ├── ai.ts                        # AI service types
│   └── global.ts                    # Global type definitions
├── hooks/                           # Custom React hooks
│   ├── use-auth.ts                  # Authentication hook
│   ├── use-transactions.ts          # Transaction management hook
│   ├── use-ai-chat.ts               # AI chat hook
│   └── use-setup.ts                 # Setup flow hook
├── middleware.ts                    # Next.js middleware (auth)
├── next.config.js                   # Next.js configuration
├── tailwind.config.js               # Tailwind CSS configuration
├── tsconfig.json                    # TypeScript configuration
├── package.json                     # Dependencies and scripts
├── .env.local                       # Environment variables
├── .env.example                     # Environment variables template
├── .gitignore                       # Git ignore rules
├── README.md                        # Project documentation
├── CLAUDE.md                        # Tier 1 - Master AI context
│
├── [EXISTING TAILGRIDS STRUCTURE]   # Preserved TailGrids components
├── application/                     # TailGrids application components
│   ├── 404/                         # 404 page components
│   ├── blogs/                       # Blog components
│   ├── cards/                       # Card components
│   ├── contacts/                    # Contact components
│   ├── footers/                     # Footer components
│   ├── modals/                      # Modal components
│   ├── navbars/                     # Navigation components
│   ├── signin/                      # Sign-in components
│   ├── table-grids/                 # Table grid components
│   └── tables/                      # Table components
├── core-components/                 # TailGrids core components
│   ├── alerts/                      # Alert components
│   ├── avatars/                     # Avatar components
│   ├── badges/                      # Badge components
│   ├── breadcrumbs/                 # Breadcrumb components
│   ├── buttons/                     # Button components
│   ├── date-picker/                 # Date picker components
│   ├── dropdowns/                   # Dropdown components
│   ├── file-uploads/                # File upload components
│   ├── inputs/                      # Input components
│   ├── modals/                      # Modal components
│   ├── paginations/                 # Pagination components
│   ├── progress-bars/               # Progress bar components
│   ├── select/                      # Select components
│   ├── switches/                    # Switch components
│   ├── tabs/                        # Tab components
│   ├── toasts/                      # Toast components
│   └── [... all other core components]
├── marketing/                       # TailGrids marketing components
│   ├── heros/                       # Hero sections
│   ├── call-to-actions/             # CTA components
│   ├── testimonials/                # Testimonial components
│   └── [... all other marketing components]
├── assets/                          # Static assets
│   ├── css/                         # Compiled CSS
│   ├── images/                      # Image assets
│   └── js/                          # JavaScript assets
├── src/                             # Source files
│   └── tailwind.css                 # Main Tailwind CSS file
├── plugin.js                       # TailGrids plugin
└── postcss.config.js               # PostCSS configuration
```

## Key Integration Points

### TailGrids Integration
- **Plugin**: Configured via `@plugin 'tailgrids/plugin'` in `app/globals.css`
- **Components**: Direct import from `core-components/` and `application/` directories
- **Styling**: All existing TailGrids styles preserved and extended

### Database Schema (Supabase)
```sql
-- Core tables for the finance tracker
profiles (extends auth.users)
accounts
categories  
transactions
recurring_payments
loans_debits
ai_analysis_results
user_preferences
api_configurations
```

### Environment Configuration
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI Service APIs
GOOGLE_GEMINI_API_KEY=your_gemini_api_key
MISTRAL_AI_API_KEY=your_mistral_api_key

# Application Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

### MCP Server Configuration
```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["@upstash/context7"]
    },
    "gemini": {
      "command": "npx", 
      "args": ["mcp-gemini-assistant"],
      "env": {
        "GEMINI_API_KEY": "your-api-key"
      }
    }
  }
}
```

## Development Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build", 
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "build-css": "npx @tailwindcss/cli -i ./src/tailwind.css -o ./assets/css/tailwind.css -w",
    "test": "jest",
    "test:watch": "jest --watch",
    "supabase:gen-types": "supabase gen types typescript --local > types/database.ts"
  }
}
```

This structure integrates the Claude Code Development Kit framework with the existing TailGrids component library, creating a comprehensive development environment for the AI-powered personal finance tracker.
