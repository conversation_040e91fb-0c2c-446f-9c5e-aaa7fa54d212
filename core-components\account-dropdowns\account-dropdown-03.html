<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Account Dropdowns | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../../assets/css/tailwind.css" />

    <script defer src="../../assets/js/alpine.min.js"></script>
  </head>
  <body>
    <!-- ====== Account Dropdowns Section Start -->
    <section class="bg-gray-2 py-20 dark:bg-dark">
      <div class="container">
        <!-- Account Dropdown Style One -->
        <div x-data="{showDropdown: true}" class="flex justify-center">
          <div
            @click.outside="showDropdown = false"
            class="relative inline-block"
          >
            <button
              @click="showDropdown = !showDropdown"
              class="mb-3.5 inline-flex h-12 items-center justify-center gap-2 rounded-lg border border-stroke bg-white px-6 py-3 text-base font-medium text-dark dark:border-dark-3 dark:bg-dark-2 dark:text-white"
            >
              Account
              <span
                :class="showDropdown ? '-scale-y-100' : ''"
                class="duration-100"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4062 5.65625 17.6875 5.9375C17.9688 6.21875 17.9688 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1562 10.1875 14.25 10 14.25Z"
                    fill="currentColor"
                  />
                </svg>
              </span>
            </button>
            <div
              :class="showDropdown ? 'block' : 'hidden'"
              class="absolute right-0 top-full w-[240px] divide-y divide-stroke overflow-hidden rounded-lg bg-white dark:divide-dark-3 dark:bg-dark-2"
            >
              <div class="px-4 py-3">
                <p class="text-sm font-semibold text-dark dark:text-white">
                  Account menu
                </p>
              </div>
              <div>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  View profile
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Settings
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Keyboard shortcuts
                  <span class="text-xs text-dark-5"> ⌘K </span>
                </a>
              </div>
              <div>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Company profile
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Team
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Invite colleagues
                </a>
              </div>
              <div>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Changelog
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Slack Community
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Support
                </a>
                <a
                  href="javascript:void(0)"
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  API
                </a>
              </div>
              <div>
                <button
                  class="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 dark:text-white dark:hover:bg-white/5"
                >
                  Log out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- ====== Account Dropdowns Section End -->
  </body>
</html>
