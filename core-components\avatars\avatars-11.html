<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Avatars | TailGrids</title>
    <link
      rel="shortcut icon"
      href="../../assets/images/favicon.svg"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../../assets/css/tailwind.css" />

    <script defer src="../../assets/js/alpine.min.js"></script>
  </head>
  <body>
    <!-- ====== Avatars Section Start -->
    <section class="bg-white py-[75px] dark:bg-dark">
      <div class="mx-auto px-4 sm:container">
        <div class="-mx-4 flex flex-wrap justify-center">
          <div class="mb-8 w-full px-4 md:w-1/2 lg:w-1/3">
            <div x-data="{openDropDown: false}" class="relative inline-block">
              <button
                @click="openDropDown = !openDropDown"
                class="flex items-center text-left"
              >
                <div class="relative mr-4 h-[42px] w-[42px] rounded-full">
                  <img
                    src="../images/avatar/image-05.jpg"
                    alt="avatar"
                    class="h-full w-full rounded-full object-cover object-center"
                  />
                  <span
                    class="absolute -right-0.5 -top-0.5 block h-[14px] w-[14px] rounded-full border-[2.3px] border-white bg-[#219653] dark:border-dark"
                  ></span>
                </div>
                <span class="text-base font-medium text-dark dark:text-white">
                  Devid Milinear
                </span>
              </button>
              <div
                x-show="openDropDown"
                @click.outside="openDropDown = false"
                class="absolute right-0 top-full z-40 w-[200px] space-y-1 rounded-sm bg-white p-2 shadow-card dark:bg-dark-2 dark:shadow-box-dark"
              >
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Profile
                </a>
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Settings
                </a>
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Sign Out
                </a>
              </div>
            </div>
          </div>
          <div class="mb-8 w-full px-4 md:w-1/2 lg:w-1/3">
            <div x-data="{openDropDown: false}" class="relative inline-block">
              <button
                @click="openDropDown = !openDropDown"
                class="flex items-center text-left"
              >
                <div class="relative mr-4 h-[42px] w-[42px] rounded-full">
                  <img
                    src="../images/avatar/image-05.jpg"
                    alt="avatar"
                    class="h-full w-full rounded-full object-cover object-center"
                  />
                  <span
                    class="absolute -right-0.5 -top-0.5 block h-[14px] w-[14px] rounded-full border-[2.3px] border-white bg-[#219653] dark:border-dark"
                  ></span>
                </div>
                <span class="text-base font-medium text-dark dark:text-white">
                  Devid Milinear
                </span>
                <span class="pl-[10px] text-dark dark:text-white">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="fill-current"
                  >
                    <path
                      d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4062 5.65625 17.6875 5.9375C17.9688 6.21875 17.9688 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1562 10.1875 14.25 10 14.25Z"
                    />
                  </svg>
                </span>
              </button>
              <div
                x-show="openDropDown"
                @click.outside="openDropDown = false"
                class="absolute right-0 top-full z-40 w-[200px] space-y-1 rounded-sm bg-white p-2 shadow-card dark:bg-dark-2 dark:shadow-box-dark"
              >
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Profile
                </a>
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Settings
                </a>
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Sign Out
                </a>
              </div>
            </div>
          </div>
          <div class="mb-8 w-full px-4 md:w-1/2 lg:w-1/3">
            <div x-data="{openDropDown: false}" class="relative inline-block">
              <button
                @click="openDropDown = !openDropDown"
                class="flex items-center text-left"
              >
                <div class="relative mr-4 h-[42px] w-[42px] rounded-full">
                  <img
                    src="../images/avatar/image-05.jpg"
                    alt="avatar"
                    class="h-full w-full rounded-full object-cover object-center"
                  />
                  <span
                    class="absolute -right-0.5 -top-0.5 block h-[14px] w-[14px] rounded-full border-[2.3px] border-white bg-[#219653] dark:border-dark"
                  ></span>
                </div>
                <span class="text-dark dark:text-white">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="fill-current"
                  >
                    <path
                      d="M10 14.25C9.8125 14.25 9.65625 14.1875 9.5 14.0625L2.3125 7C2.03125 6.71875 2.03125 6.28125 2.3125 6C2.59375 5.71875 3.03125 5.71875 3.3125 6L10 12.5312L16.6875 5.9375C16.9688 5.65625 17.4062 5.65625 17.6875 5.9375C17.9688 6.21875 17.9688 6.65625 17.6875 6.9375L10.5 14C10.3437 14.1562 10.1875 14.25 10 14.25Z"
                    />
                  </svg>
                </span>
              </button>
              <div
                x-show="openDropDown"
                @click.outside="openDropDown = false"
                class="absolute right-0 top-full z-40 w-[200px] space-y-1 rounded-sm bg-white p-2 shadow-card dark:bg-dark-2 dark:shadow-box-dark"
              >
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Profile
                </a>
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Settings
                </a>
                <a
                  href="javascript:void(0)"
                  class="block w-full rounded-sm px-3 py-2 text-left text-sm text-body-color hover:bg-gray-2 dark:text-dark-6 dark:hover:bg-dark-3"
                >
                  Sign Out
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- ====== Avatars Section End -->
  </body>
</html>
