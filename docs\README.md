# Documentation System Guide

## Overview
This documentation system implements the Claude Code Development Kit's 3-tier architecture optimized for AI-assisted development of the AI-powered personal finance tracker.

## Documentation Architecture

### Tier 1 - Foundation (Auto-loaded)
**Purpose**: Core project context loaded with every Claude Code session
**Location**: `/docs/ai-context/`

- `project-structure.md` - Complete technology stack and file organization
- `docs-overview.md` - This guide explaining the documentation system
- `system-integration.md` - Cross-component integration patterns
- `deployment-infrastructure.md` - Infrastructure and deployment context
- `handoff.md` - Session continuity and team collaboration

### Tier 2 - Component Context
**Purpose**: Component-specific development context
**Location**: Component directories with `CLAUDE.md` files

- `/components/CLAUDE.md` - React component standards and TailGrids integration
- `/lib/CLAUDE.md` - Utility functions and service integrations
- `/hooks/CLAUDE.md` - Custom React hooks patterns
- `/types/CLAUDE.md` - TypeScript definitions and schemas

### Tier 3 - Feature Context
**Purpose**: Feature-specific implementation details
**Location**: Feature directories with `CLAUDE.md` files

- `/app/[feature]/CLAUDE.md` - Feature-specific implementation patterns
- Route-specific context for each major application feature

## Usage Instructions

### For Developers
1. **Starting a session**: Foundation documentation auto-loads
2. **Working on components**: Tier 2 context loads automatically
3. **Feature development**: Tier 3 context loads based on directory
4. **Documentation updates**: Use `/update-docs` command

### For AI Assistants
1. **Context loading**: Follows automatic rules based on task complexity
2. **Command execution**: Loads appropriate documentation tiers
3. **Cross-tier references**: Links maintained between all tiers
4. **Memory management**: Optimized for token efficiency

### For Team Collaboration
1. **Handoffs**: Use handoff documentation for session continuity
2. **Onboarding**: Start with Tier 1 documentation
3. **Feature work**: Focus on relevant Tier 2 and Tier 3 contexts
4. **Knowledge sharing**: Update documentation as patterns evolve

## Documentation Maintenance

### Automated Updates
- `/update-docs` command maintains currency across all tiers
- Git hooks update relevant documentation on commits
- CI/CD pipeline validates documentation completeness

### Manual Reviews
- Weekly Tier 1 accuracy reviews
- Feature completion triggers Tier 3 updates
- Quarterly comprehensive documentation audits

## Best Practices

### Writing Documentation
- Keep Tier 1 concise but comprehensive
- Make Tier 2 action-oriented for component work
- Ensure Tier 3 provides sufficient implementation detail
- Use consistent formatting and terminology

### Maintaining Accuracy
- Update documentation immediately after code changes
- Validate examples and code snippets regularly
- Cross-reference between tiers for consistency
- Remove obsolete information promptly

### Optimizing for AI
- Structure information hierarchically
- Use clear headings and sections
- Provide examples and patterns
- Include context for decision-making

## Integration with TailGrids

The documentation system includes specific guidance for integrating with the existing TailGrids component library:

- Component mapping between features and TailGrids elements
- Styling patterns and customization guidelines
- Responsive design implementation using TailGrids
- Accessibility standards with TailGrids components

## Claude Code Commands

The system provides specialized commands for documentation management:

- `/docs-overview` - Show this documentation guide
- `/docs-health` - Check documentation completeness
- `/update-docs` - Update all documentation files
- `/docs-export` - Export documentation for external use

This system ensures optimal AI assistance while maintaining excellent developer experience and team collaboration.
