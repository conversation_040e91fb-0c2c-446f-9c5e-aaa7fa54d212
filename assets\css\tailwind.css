/*! tailwindcss v4.0.9 | MIT License | https://tailwindcss.com */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap') layer(base);
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xl: 36rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: "Inter", sans-serif, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --default-font-feature-settings: normal;
    --default-font-variation-settings: normal;
    --default-mono-font-family: var(--font-mono);
    --default-mono-font-feature-settings: var(
      --font-mono--font-feature-settings
    );
    --default-mono-font-variation-settings: var(
      --font-mono--font-variation-settings
    );
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var( --default-font-variation-settings, normal );
    -webkit-tap-highlight-color: transparent;
  }
  body {
    line-height: inherit;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace );
    font-feature-settings: var( --default-mono-font-feature-settings, normal );
    font-variation-settings: var( --default-mono-font-variation-settings, normal );
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
    color: color-mix(in oklab, currentColor 50%, transparent);
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-0\.5 {
    top: calc(var(--spacing) * -0.5);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .-top-2 {
    top: calc(var(--spacing) * -2);
  }
  .-top-6 {
    top: calc(var(--spacing) * -6);
  }
  .-top-7 {
    top: calc(var(--spacing) * -7);
  }
  .-top-10 {
    top: calc(var(--spacing) * -10);
  }
  .-top-16 {
    top: calc(var(--spacing) * -16);
  }
  .-top-\[6px\] {
    top: calc(6px * -1);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-5 {
    top: calc(var(--spacing) * 5);
  }
  .top-7 {
    top: calc(var(--spacing) * 7);
  }
  .top-10 {
    top: calc(var(--spacing) * 10);
  }
  .top-\[-3px\] {
    top: -3px;
  }
  .top-\[90px\] {
    top: 90px;
  }
  .top-\[110\%\] {
    top: 110%;
  }
  .top-full {
    top: 100%;
  }
  .-right-0\.5 {
    right: calc(var(--spacing) * -0.5);
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .-right-2 {
    right: calc(var(--spacing) * -2);
  }
  .-right-4 {
    right: calc(var(--spacing) * -4);
  }
  .-right-6 {
    right: calc(var(--spacing) * -6);
  }
  .-right-7 {
    right: calc(var(--spacing) * -7);
  }
  .-right-9 {
    right: calc(var(--spacing) * -9);
  }
  .-right-10 {
    right: calc(var(--spacing) * -10);
  }
  .-right-\[6px\] {
    right: calc(6px * -1);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-5 {
    right: calc(var(--spacing) * 5);
  }
  .right-10 {
    right: calc(var(--spacing) * 10);
  }
  .right-\[-3px\] {
    right: -3px;
  }
  .right-\[18px\] {
    right: 18px;
  }
  .right-full {
    right: 100%;
  }
  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }
  .-bottom-5 {
    bottom: calc(var(--spacing) * -5);
  }
  .-bottom-6 {
    bottom: calc(var(--spacing) * -6);
  }
  .-bottom-7 {
    bottom: calc(var(--spacing) * -7);
  }
  .-bottom-8 {
    bottom: calc(var(--spacing) * -8);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-5 {
    bottom: calc(var(--spacing) * 5);
  }
  .bottom-\[-2px\] {
    bottom: -2px;
  }
  .bottom-\[-3px\] {
    bottom: -3px;
  }
  .bottom-full {
    bottom: 100%;
  }
  .-left-1 {
    left: calc(var(--spacing) * -1);
  }
  .-left-6 {
    left: calc(var(--spacing) * -6);
  }
  .-left-7 {
    left: calc(var(--spacing) * -7);
  }
  .-left-8 {
    left: calc(var(--spacing) * -8);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1 {
    left: calc(var(--spacing) * 1);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-\[-3px\] {
    left: -3px;
  }
  .left-full {
    left: 100%;
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[-1\] {
    z-index: -1;
  }
  .z-\[-2\] {
    z-index: -2;
  }
  .container {
    width: 100%;
    @media (width >= 400px) {
      max-width: 400px;
    }
    @media (width >= 540px) {
      max-width: 540px;
    }
    @media (width >= 720px) {
      max-width: 720px;
    }
    @media (width >= 960px) {
      max-width: 960px;
    }
    @media (width >= 1140px) {
      max-width: 1140px;
    }
    @media (width >= 1320px) {
      max-width: 1320px;
    }
  }
  .m-2 {
    margin: calc(var(--spacing) * 2);
  }
  .m-10 {
    margin: calc(var(--spacing) * 10);
  }
  .container {
    margin-inline: auto;
    padding-inline: 16px;
  }
  .-mx-2 {
    margin-inline: calc(var(--spacing) * -2);
  }
  .-mx-3 {
    margin-inline: calc(var(--spacing) * -3);
  }
  .-mx-4 {
    margin-inline: calc(var(--spacing) * -4);
  }
  .-mx-5 {
    margin-inline: calc(var(--spacing) * -5);
  }
  .-mx-\[6px\] {
    margin-inline: calc(6px * -1);
  }
  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }
  .mx-7 {
    margin-inline: calc(var(--spacing) * 7);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }
  .my-\[6px\] {
    margin-block: 6px;
  }
  .-mt-2 {
    margin-top: calc(var(--spacing) * -2);
  }
  .-mt-20 {
    margin-top: calc(var(--spacing) * -20);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .-mr-3 {
    margin-right: calc(var(--spacing) * -3);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-2\.5 {
    margin-right: calc(var(--spacing) * 2.5);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-5 {
    margin-right: calc(var(--spacing) * 5);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .mr-8 {
    margin-right: calc(var(--spacing) * 8);
  }
  .mr-\[6px\] {
    margin-right: 6px;
  }
  .mr-\[18px\] {
    margin-right: 18px;
  }
  .-mb-\[1px\] {
    margin-bottom: calc(1px * -1);
  }
  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-3\.5 {
    margin-bottom: calc(var(--spacing) * 3.5);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-7 {
    margin-bottom: calc(var(--spacing) * 7);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-9 {
    margin-bottom: calc(var(--spacing) * 9);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-11 {
    margin-bottom: calc(var(--spacing) * 11);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-14 {
    margin-bottom: calc(var(--spacing) * 14);
  }
  .mb-\[10px\] {
    margin-bottom: 10px;
  }
  .mb-\[14px\] {
    margin-bottom: 14px;
  }
  .mb-\[60px\] {
    margin-bottom: 60px;
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }
  .-ml-5 {
    margin-left: calc(var(--spacing) * -5);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-\[2px\] {
    height: 2px;
  }
  .h-\[3px\] {
    height: 3px;
  }
  .h-\[7px\] {
    height: 7px;
  }
  .h-\[10px\] {
    height: 10px;
  }
  .h-\[13px\] {
    height: 13px;
  }
  .h-\[14px\] {
    height: 14px;
  }
  .h-\[15px\] {
    height: 15px;
  }
  .h-\[18px\] {
    height: 18px;
  }
  .h-\[26px\] {
    height: 26px;
  }
  .h-\[30px\] {
    height: 30px;
  }
  .h-\[34px\] {
    height: 34px;
  }
  .h-\[36px\] {
    height: 36px;
  }
  .h-\[37px\] {
    height: 37px;
  }
  .h-\[38px\] {
    height: 38px;
  }
  .h-\[42px\] {
    height: 42px;
  }
  .h-\[44px\] {
    height: 44px;
  }
  .h-\[45px\] {
    height: 45px;
  }
  .h-\[46px\] {
    height: 46px;
  }
  .h-\[50px\] {
    height: 50px;
  }
  .h-\[52px\] {
    height: 52px;
  }
  .h-\[54px\] {
    height: 54px;
  }
  .h-\[60px\] {
    height: 60px;
  }
  .h-\[70px\] {
    height: 70px;
  }
  .h-\[200px\] {
    height: 200px;
  }
  .h-\[262px\] {
    height: 262px;
  }
  .h-\[300px\] {
    height: 300px;
  }
  .h-\[320px\] {
    height: 320px;
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-px {
    height: 1px;
  }
  .h-screen {
    height: 100vh;
  }
  .min-h-\[260px\] {
    min-height: 260px;
  }
  .min-h-\[328px\] {
    min-height: 328px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\/6 {
    width: calc(1/6 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-4\/6 {
    width: calc(4/6 * 100%);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-5\/6 {
    width: calc(5/6 * 100%);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-60 {
    width: calc(var(--spacing) * 60);
  }
  .w-\[7px\] {
    width: 7px;
  }
  .w-\[10\%\] {
    width: 10%;
  }
  .w-\[10px\] {
    width: 10px;
  }
  .w-\[13px\] {
    width: 13px;
  }
  .w-\[14px\] {
    width: 14px;
  }
  .w-\[15px\] {
    width: 15px;
  }
  .w-\[18px\] {
    width: 18px;
  }
  .w-\[20\%\] {
    width: 20%;
  }
  .w-\[30px\] {
    width: 30px;
  }
  .w-\[38px\] {
    width: 38px;
  }
  .w-\[40\%\] {
    width: 40%;
  }
  .w-\[40px\] {
    width: 40px;
  }
  .w-\[42px\] {
    width: 42px;
  }
  .w-\[45\%\] {
    width: 45%;
  }
  .w-\[46px\] {
    width: 46px;
  }
  .w-\[50\%\] {
    width: 50%;
  }
  .w-\[50px\] {
    width: 50px;
  }
  .w-\[52px\] {
    width: 52px;
  }
  .w-\[60px\] {
    width: 60px;
  }
  .w-\[63\%\] {
    width: 63%;
  }
  .w-\[64px\] {
    width: 64px;
  }
  .w-\[68px\] {
    width: 68px;
  }
  .w-\[70px\] {
    width: 70px;
  }
  .w-\[75\%\] {
    width: 75%;
  }
  .w-\[82px\] {
    width: 82px;
  }
  .w-\[84\%\] {
    width: 84%;
  }
  .w-\[90\%\] {
    width: 90%;
  }
  .w-\[90px\] {
    width: 90px;
  }
  .w-\[150px\] {
    width: 150px;
  }
  .w-\[162px\] {
    width: 162px;
  }
  .w-\[200px\] {
    width: 200px;
  }
  .w-\[240px\] {
    width: 240px;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .max-w-\[8px\] {
    max-width: 8px;
  }
  .max-w-\[26px\] {
    max-width: 26px;
  }
  .max-w-\[30px\] {
    max-width: 30px;
  }
  .max-w-\[34px\] {
    max-width: 34px;
  }
  .max-w-\[36px\] {
    max-width: 36px;
  }
  .max-w-\[40px\] {
    max-width: 40px;
  }
  .max-w-\[44px\] {
    max-width: 44px;
  }
  .max-w-\[45px\] {
    max-width: 45px;
  }
  .max-w-\[46px\] {
    max-width: 46px;
  }
  .max-w-\[54px\] {
    max-width: 54px;
  }
  .max-w-\[60px\] {
    max-width: 60px;
  }
  .max-w-\[160px\] {
    max-width: 160px;
  }
  .max-w-\[210px\] {
    max-width: 210px;
  }
  .max-w-\[220px\] {
    max-width: 220px;
  }
  .max-w-\[250px\] {
    max-width: 250px;
  }
  .max-w-\[290px\] {
    max-width: 290px;
  }
  .max-w-\[300px\] {
    max-width: 300px;
  }
  .max-w-\[310px\] {
    max-width: 310px;
  }
  .max-w-\[325px\] {
    max-width: 325px;
  }
  .max-w-\[350px\] {
    max-width: 350px;
  }
  .max-w-\[370px\] {
    max-width: 370px;
  }
  .max-w-\[380px\] {
    max-width: 380px;
  }
  .max-w-\[400px\] {
    max-width: 400px;
  }
  .max-w-\[415px\] {
    max-width: 415px;
  }
  .max-w-\[422px\] {
    max-width: 422px;
  }
  .max-w-\[450px\] {
    max-width: 450px;
  }
  .max-w-\[460px\] {
    max-width: 460px;
  }
  .max-w-\[470px\] {
    max-width: 470px;
  }
  .max-w-\[480px\] {
    max-width: 480px;
  }
  .max-w-\[490px\] {
    max-width: 490px;
  }
  .max-w-\[500px\] {
    max-width: 500px;
  }
  .max-w-\[510px\] {
    max-width: 510px;
  }
  .max-w-\[520px\] {
    max-width: 520px;
  }
  .max-w-\[525px\] {
    max-width: 525px;
  }
  .max-w-\[550px\] {
    max-width: 550px;
  }
  .max-w-\[560px\] {
    max-width: 560px;
  }
  .max-w-\[570px\] {
    max-width: 570px;
  }
  .max-w-\[650px\] {
    max-width: 650px;
  }
  .max-w-\[655px\] {
    max-width: 655px;
  }
  .max-w-\[700px\] {
    max-width: 700px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }
  .min-w-\[160px\] {
    min-width: 160px;
  }
  .min-w-\[300px\] {
    min-width: 300px;
  }
  .flex-1 {
    flex: 1;
  }
  .table-auto {
    table-layout: auto;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-1\/2 {
    --tw-translate-x: calc(1/2 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-scale-y-100 {
    --tw-scale-y: calc(100% * -1);
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .-rotate-45 {
    rotate: calc(45deg * -1);
  }
  .rotate-45 {
    rotate: 45deg;
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .animate-ping {
    animation: var(--animate-ping);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize-none {
    resize: none;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-disc {
    list-style-type: disc;
  }
  .appearance-none {
    appearance: none;
  }
  .break-inside-avoid {
    break-inside: avoid;
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-\[5px\] {
    gap: 5px;
  }
  .gap-\[14px\] {
    gap: 14px;
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-7 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 7) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 7) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-10 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-11 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 11) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 11) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-20 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 20) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 20) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-\[6px\] {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(6px * var(--tw-space-x-reverse));
      margin-inline-end: calc(6px * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-\[10px\] {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(10px * var(--tw-space-x-reverse));
      margin-inline-end: calc(10px * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * 0.5);
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-stroke {
    :where(& > :not(:last-child)) {
      border-color: #DFE4EA;
    }
  }
  .self-center {
    align-self: center;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-\[3px\] {
    border-radius: 3px;
  }
  .rounded-\[5px\] {
    border-radius: 5px;
  }
  .rounded-\[7px\] {
    border-radius: 7px;
  }
  .rounded-\[10px\] {
    border-radius: 10px;
  }
  .rounded-\[15px\] {
    border-radius: 15px;
  }
  .rounded-\[20px\] {
    border-radius: 20px;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-xs {
    border-radius: var(--radius-xs);
  }
  .rounded-t-xl {
    border-top-left-radius: var(--radius-xl);
    border-top-right-radius: var(--radius-xl);
  }
  .rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .rounded-b-xl {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-\[\.5px\] {
    border-style: var(--tw-border-style);
    border-width: .5px;
  }
  .border-\[2\.3px\] {
    border-style: var(--tw-border-style);
    border-width: 2.3px;
  }
  .border-\[2\.7px\] {
    border-style: var(--tw-border-style);
    border-width: 2.7px;
  }
  .border-\[3px\] {
    border-style: var(--tw-border-style);
    border-width: 3px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-b-\[3px\] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 3px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-\[5px\] {
    border-left-style: var(--tw-border-style);
    border-left-width: 5px;
  }
  .border-l-\[6px\] {
    border-left-style: var(--tw-border-style);
    border-left-width: 6px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-\[\#BFCEFF\] {
    border-color: #BFCEFF;
  }
  .border-\[\#E4E4E4\] {
    border-color: #E4E4E4;
  }
  .border-\[\#E8E8E8\] {
    border-color: #E8E8E8;
  }
  .border-\[\#b7b7b7\] {
    border-color: #b7b7b7;
  }
  .border-blue-dark {
    border-color: #1C3FB7;
  }
  .border-body-color {
    border-color: #637381;
  }
  .border-current {
    border-color: currentColor;
  }
  .border-cyan {
    border-color: #01A9DB;
  }
  .border-cyan-dark {
    border-color: #0B76B7;
  }
  .border-dark {
    border-color: #111928;
  }
  .border-dark-6 {
    border-color: #9CA3AF;
  }
  .border-gray-1 {
    border-color: #F9FAFB;
  }
  .border-gray-2 {
    border-color: #F3F4F6;
  }
  .border-gray-3 {
    border-color: #E5E7EB;
  }
  .border-green {
    border-color: #22AD5C;
  }
  .border-green-dark {
    border-color: #1A8245;
  }
  .border-green-light-4 {
    border-color: #ACEFC8;
  }
  .border-light {
    border-color: #efefef;
  }
  .border-primary {
    border-color: #3758F9;
  }
  .border-primary\/30 {
    border-color: color-mix(in oklab, #3758F9 30%, transparent);
  }
  .border-red {
    border-color: #F23030;
  }
  .border-red-dark {
    border-color: #E10E0E;
  }
  .border-red-light-4 {
    border-color: #FDD8D8;
  }
  .border-secondary {
    border-color: #13C296;
  }
  .border-stroke {
    border-color: #DFE4EA;
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-yellow {
    border-color: #FBBF24;
  }
  .border-yellow-dark {
    border-color: #F59E0B;
  }
  .border-l-cyan-dark {
    border-left-color: #0B76B7;
  }
  .border-l-yellow {
    border-left-color: #FBBF24;
  }
  .bg-\[\#1C9CEA\] {
    background-color: #1C9CEA;
  }
  .bg-\[\#4064AC\] {
    background-color: #4064AC;
  }
  .bg-\[\#219653\] {
    background-color: #219653;
  }
  .bg-\[\#CCCCCE\] {
    background-color: #CCCCCE;
  }
  .bg-\[\#D64937\] {
    background-color: #D64937;
  }
  .bg-\[\#E4E4E4\] {
    background-color: #E4E4E4;
  }
  .bg-\[\#E9F9FF\] {
    background-color: #E9F9FF;
  }
  .bg-\[\#F0F4FF\] {
    background-color: #F0F4FF;
  }
  .bg-\[\#F3F6FF\] {
    background-color: #F3F6FF;
  }
  .bg-\[\#FFA645\] {
    background-color: #FFA645;
  }
  .bg-black {
    background-color: #212B36;
  }
  .bg-black\/70 {
    background-color: color-mix(in oklab, #212B36 70%, transparent);
  }
  .bg-blue-light-5 {
    background-color: #E1E8FF;
  }
  .bg-body-color {
    background-color: #637381;
  }
  .bg-body-color\/10 {
    background-color: color-mix(in oklab, #637381 10%, transparent);
  }
  .bg-cyan {
    background-color: #01A9DB;
  }
  .bg-cyan-dark {
    background-color: #0B76B7;
  }
  .bg-cyan-light-2\/30 {
    background-color: color-mix(in oklab, #77D1F3 30%, transparent);
  }
  .bg-cyan\/10 {
    background-color: color-mix(in oklab, #01A9DB 10%, transparent);
  }
  .bg-dark {
    background-color: #111928;
  }
  .bg-dark-3 {
    background-color: #374151;
  }
  .bg-dark\/10 {
    background-color: color-mix(in oklab, #111928 10%, transparent);
  }
  .bg-dark\/90 {
    background-color: color-mix(in oklab, #111928 90%, transparent);
  }
  .bg-gray {
    background-color: #F9FAFB;
  }
  .bg-gray-1 {
    background-color: #F9FAFB;
  }
  .bg-gray-2 {
    background-color: #F3F4F6;
  }
  .bg-gray-3 {
    background-color: #E5E7EB;
  }
  .bg-gray-3\/50 {
    background-color: color-mix(in oklab, #E5E7EB 50%, transparent);
  }
  .bg-green {
    background-color: #22AD5C;
  }
  .bg-green-dark {
    background-color: #1A8245;
  }
  .bg-green-dark\/10 {
    background-color: color-mix(in oklab, #1A8245 10%, transparent);
  }
  .bg-green-light-6 {
    background-color: #DAF8E6;
  }
  .bg-green\/10 {
    background-color: color-mix(in oklab, #22AD5C 10%, transparent);
  }
  .bg-inherit {
    background-color: inherit;
  }
  .bg-primary {
    background-color: #3758F9;
  }
  .bg-primary\/5 {
    background-color: color-mix(in oklab, #3758F9 5%, transparent);
  }
  .bg-primary\/10 {
    background-color: color-mix(in oklab, #3758F9 10%, transparent);
  }
  .bg-primary\/60 {
    background-color: color-mix(in oklab, #3758F9 60%, transparent);
  }
  .bg-primary\/90 {
    background-color: color-mix(in oklab, #3758F9 90%, transparent);
  }
  .bg-red {
    background-color: #F23030;
  }
  .bg-red-dark {
    background-color: #E10E0E;
  }
  .bg-red-dark\/10 {
    background-color: color-mix(in oklab, #E10E0E 10%, transparent);
  }
  .bg-red-light-5 {
    background-color: #FEEBEB;
  }
  .bg-red-light-6 {
    background-color: #FEF3F3;
  }
  .bg-secondary {
    background-color: #13C296;
  }
  .bg-secondary\/10 {
    background-color: color-mix(in oklab, #13C296 10%, transparent);
  }
  .bg-stroke {
    background-color: #DFE4EA;
  }
  .bg-tg-bg {
    background-color: #f7f8fa;
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/10 {
    background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
  }
  .bg-white\/20 {
    background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
  }
  .bg-yellow {
    background-color: #FBBF24;
  }
  .bg-yellow-dark {
    background-color: #F59E0B;
  }
  .bg-yellow-dark\/10 {
    background-color: color-mix(in oklab, #F59E0B 10%, transparent);
  }
  .bg-yellow-light-4 {
    background-color: #FFFBEB;
  }
  .bg-yellow\/30 {
    background-color: color-mix(in oklab, #FBBF24 30%, transparent);
  }
  .bg-linear-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-\[\#27F090\] {
    --tw-gradient-from: #27F090;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#FFFFFF14\] {
    --tw-gradient-from: #FFFFFF14;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-1 {
    --tw-gradient-from: #F9FAFB;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-\[\#7F41F3\] {
    --tw-gradient-via: #7F41F3;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-\[\#C4C4C400\] {
    --tw-gradient-to: #C4C4C400;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#F59527\] {
    --tw-gradient-to: #F59527;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-4 {
    --tw-gradient-to: #DEE2E6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .fill-current {
    fill: currentColor;
  }
  .stroke-current {
    stroke: currentColor;
  }
  .object-cover {
    object-fit: cover;
  }
  .object-center {
    object-position: center;
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-10 {
    padding: calc(var(--spacing) * 10);
  }
  .p-\[25px\] {
    padding: 25px;
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-3\.5 {
    padding-inline: calc(var(--spacing) * 3.5);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-7 {
    padding-inline: calc(var(--spacing) * 7);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }
  .px-\[6px\] {
    padding-inline: 6px;
  }
  .px-\[14px\] {
    padding-inline: 14px;
  }
  .px-\[18px\] {
    padding-inline: 18px;
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-9 {
    padding-block: calc(var(--spacing) * 9);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }
  .py-\[5px\] {
    padding-block: 5px;
  }
  .py-\[6px\] {
    padding-block: 6px;
  }
  .py-\[10px\] {
    padding-block: 10px;
  }
  .py-\[13px\] {
    padding-block: 13px;
  }
  .py-\[18px\] {
    padding-block: 18px;
  }
  .py-\[34px\] {
    padding-block: 34px;
  }
  .py-\[60px\] {
    padding-block: 60px;
  }
  .py-\[70px\] {
    padding-block: 70px;
  }
  .py-\[75px\] {
    padding-block: 75px;
  }
  .py-\[120px\] {
    padding-block: 120px;
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-2\.5 {
    padding-top: calc(var(--spacing) * 2.5);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-11 {
    padding-top: calc(var(--spacing) * 11);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }
  .pt-\[120px\] {
    padding-top: 120px;
  }
  .pr-0\.5 {
    padding-right: calc(var(--spacing) * 0.5);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }
  .pr-6 {
    padding-right: calc(var(--spacing) * 6);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }
  .pr-14 {
    padding-right: calc(var(--spacing) * 14);
  }
  .pr-16 {
    padding-right: calc(var(--spacing) * 16);
  }
  .pr-\[10px\] {
    padding-right: 10px;
  }
  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pb-\[18px\] {
    padding-bottom: 18px;
  }
  .pb-\[84px\] {
    padding-bottom: 84px;
  }
  .pb-\[110px\] {
    padding-bottom: 110px;
  }
  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }
  .pl-7 {
    padding-left: calc(var(--spacing) * 7);
  }
  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }
  .pl-\[10px\] {
    padding-left: 10px;
  }
  .pl-\[50px\] {
    padding-left: 50px;
  }
  .pl-\[62px\] {
    padding-left: 62px;
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[22px\] {
    font-size: 22px;
  }
  .text-\[26px\] {
    font-size: 26px;
  }
  .text-\[32px\] {
    font-size: 32px;
  }
  .text-\[42px\] {
    font-size: 42px;
  }
  .text-\[50px\] {
    font-size: 50px;
  }
  .leading-\[1\.2\] {
    --tw-leading: 1.2;
    line-height: 1.2;
  }
  .leading-\[1\.81\] {
    --tw-leading: 1.81;
    line-height: 1.81;
  }
  .leading-\[1\.208\] {
    --tw-leading: 1.208;
    line-height: 1.208;
  }
  .leading-\[1\.208\]\! {
    --tw-leading: 1.208;
    line-height: 1.208 !important;
  }
  .leading-\[27px\] {
    --tw-leading: 27px;
    line-height: 27px;
  }
  .leading-loose {
    --tw-leading: var(--leading-loose);
    line-height: var(--leading-loose);
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-\[\#9D5425\] {
    color: #9D5425;
  }
  .text-\[\#004434\] {
    color: #004434;
  }
  .text-\[\#464646\] {
    color: #464646;
  }
  .text-\[\#BC1C21\] {
    color: #BC1C21;
  }
  .text-\[\#D0915C\] {
    color: #D0915C;
  }
  .text-black {
    color: #212B36;
  }
  .text-body-color {
    color: #637381;
  }
  .text-cyan {
    color: #01A9DB;
  }
  .text-cyan-dark {
    color: #0B76B7;
  }
  .text-dark {
    color: #111928;
  }
  .text-dark-2 {
    color: #1F2A37;
  }
  .text-dark-3 {
    color: #374151;
  }
  .text-dark-4 {
    color: #4B5563;
  }
  .text-dark-5 {
    color: #6B7280;
  }
  .text-dark-6 {
    color: #9CA3AF;
  }
  .text-gray-5 {
    color: #CED4DA;
  }
  .text-green {
    color: #22AD5C;
  }
  .text-green-dark {
    color: #1A8245;
  }
  .text-primary {
    color: #3758F9;
  }
  .text-red {
    color: #F23030;
  }
  .text-red-dark {
    color: #E10E0E;
  }
  .text-red-light {
    color: #F56060;
  }
  .text-secondary {
    color: #13C296;
  }
  .text-secondary-color {
    color: #8899A8;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-white\/50 {
    color: color-mix(in oklab, var(--color-white) 50%, transparent);
  }
  .text-white\/60 {
    color: color-mix(in oklab, var(--color-white) 60%, transparent);
  }
  .text-white\/80 {
    color: color-mix(in oklab, var(--color-white) 80%, transparent);
  }
  .text-yellow-dark {
    color: #F59E0B;
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .underline {
    text-decoration-line: underline;
  }
  .underline-offset-2 {
    text-underline-offset: 2px;
  }
  .placeholder-dark-6 {
    &::placeholder {
      color: #9CA3AF;
    }
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-40 {
    opacity: 40%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-1 {
    --tw-shadow: 0px 1px 3px 0px var(--tw-shadow-color, rgba(166, 175, 195, 0.40));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2 {
    --tw-shadow: 0px 5px 12px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.10));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0px_1px_3px_0px_rgba\(0\,0\,0\,0\.13\)\] {
    --tw-shadow: 0px 1px 3px 0px var(--tw-shadow-color, rgba(0,0,0,0.13));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0px_2px_10px_0px_rgba\(0\,0\,0\,0\.08\)\] {
    --tw-shadow: 0px 2px 10px 0px var(--tw-shadow-color, rgba(0,0,0,0.08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0px_20px_95px_0px_rgba\(201\,203\,204\,0\.30\)\] {
    --tw-shadow: 0px 20px 95px 0px var(--tw-shadow-color, rgba(201,203,204,0.30));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-card {
    --tw-shadow: 0px 1px 3px var(--tw-shadow-color, rgba(0, 0, 0, 0.12));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-four {
    --tw-shadow: 0px 4px 10px var(--tw-shadow-color, rgba(0, 0, 0, 0.12));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-portfolio {
    --tw-shadow: 0px 4px 30px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-pricing {
    --tw-shadow: 0px 39px 23px -27px var(--tw-shadow-color, rgba(0, 0, 0, 0.04));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-pricing-4 {
    --tw-shadow: 0px 1px 4px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.12));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0px 1px 3px 0px var(--tw-shadow-color, rgba(16, 24, 40, 0.10)), 0px 1px 2px 0px var(--tw-shadow-color, rgba(16, 24, 40, 0.06));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-switch-1 {
    --tw-shadow: 0px 0px 5px var(--tw-shadow-color, rgba(0, 0, 0, 0.15));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-testimonial-6 {
    --tw-shadow: 0px 10px 20px 0px var(--tw-shadow-color, rgba(92, 115, 160, 0.07));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-three {
    --tw-shadow: 0px 1px 5px var(--tw-shadow-color, rgba(0, 0, 0, 0.14));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-two {
    --tw-shadow: 0px 1px 4px var(--tw-shadow-color, rgba(0, 0, 0, 0.12));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, rgba(16, 24, 40, 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-primary {
    --tw-ring-color: #3758F9;
  }
  .outline-hidden {
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .delay-300 {
    transition-delay: 300ms;
  }
  .duration-100 {
    --tw-duration: 100ms;
    transition-duration: 100ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-1000 {
    --tw-duration: 1000ms;
    transition-duration: 1000ms;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .group-hover\:border-primary {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: #3758F9;
      }
    }
  }
  .group-hover\:border-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: var(--color-white);
      }
    }
  }
  .group-hover\:bg-primary {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: #3758F9;
      }
    }
  }
  .group-hover\:bg-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .group-hover\:text-body-color {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: #637381;
      }
    }
  }
  .group-hover\:text-primary {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: #3758F9;
      }
    }
  }
  .group-hover\:text-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-has-checked\:bg-primary {
    &:is(:where(.group):has(*:checked) *) {
      background-color: #3758F9;
    }
  }
  .group-has-checked\:bg-white {
    &:is(:where(.group):has(*:checked) *) {
      background-color: var(--color-white);
    }
  }
  .group-has-checked\:opacity-100 {
    &:is(:where(.group):has(*:checked) *) {
      opacity: 100%;
    }
  }
  .peer-checked\:translate-x-full {
    &:is(:where(.peer):checked ~ *) {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .peer-checked\:border-primary {
    &:is(:where(.peer):checked ~ *) {
      border-color: #3758F9;
    }
  }
  .peer-checked\:bg-\[\#EAEEFB\] {
    &:is(:where(.peer):checked ~ *) {
      background-color: #EAEEFB;
    }
  }
  .peer-checked\:bg-primary {
    &:is(:where(.peer):checked ~ *) {
      background-color: #3758F9;
    }
  }
  .peer-checked\:bg-primary\/10 {
    &:is(:where(.peer):checked ~ *) {
      background-color: color-mix(in oklab, #3758F9 10%, transparent);
    }
  }
  .peer-checked\:bg-white {
    &:is(:where(.peer):checked ~ *) {
      background-color: var(--color-white);
    }
  }
  .peer-checked\:text-white {
    &:is(:where(.peer):checked ~ *) {
      color: var(--color-white);
    }
  }
  .selection\:bg-transparent {
    & *::selection {
      background-color: transparent;
    }
    &::selection {
      background-color: transparent;
    }
  }
  .file\:mr-4 {
    &::file-selector-button {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .file\:mr-5 {
    &::file-selector-button {
      margin-right: calc(var(--spacing) * 5);
    }
  }
  .file\:border-collapse {
    &::file-selector-button {
      border-collapse: collapse;
    }
  }
  .file\:cursor-pointer {
    &::file-selector-button {
      cursor: pointer;
    }
  }
  .file\:rounded-sm {
    &::file-selector-button {
      border-radius: var(--radius-sm);
    }
  }
  .file\:border-0 {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .file\:border-\[\.5px\] {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: .5px;
    }
  }
  .file\:border-stroke {
    &::file-selector-button {
      border-color: #DFE4EA;
    }
  }
  .file\:bg-dark-2 {
    &::file-selector-button {
      background-color: #1F2A37;
    }
  }
  .file\:bg-gray-3 {
    &::file-selector-button {
      background-color: #E5E7EB;
    }
  }
  .file\:px-3 {
    &::file-selector-button {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .file\:px-4 {
    &::file-selector-button {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .file\:py-1 {
    &::file-selector-button {
      padding-block: calc(var(--spacing) * 1);
    }
  }
  .file\:py-3 {
    &::file-selector-button {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .file\:text-base {
    &::file-selector-button {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .file\:text-dark {
    &::file-selector-button {
      color: #111928;
    }
  }
  .file\:text-white {
    &::file-selector-button {
      color: var(--color-white);
    }
  }
  .after\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\:-bottom-1\.5 {
    &::after {
      content: var(--tw-content);
      bottom: calc(var(--spacing) * -1.5);
    }
  }
  .after\:left-1\/2 {
    &::after {
      content: var(--tw-content);
      left: calc(1/2 * 100%);
    }
  }
  .after\:h-3 {
    &::after {
      content: var(--tw-content);
      height: calc(var(--spacing) * 3);
    }
  }
  .after\:w-3 {
    &::after {
      content: var(--tw-content);
      width: calc(var(--spacing) * 3);
    }
  }
  .after\:-translate-x-1\/2 {
    &::after {
      content: var(--tw-content);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .after\:rotate-45 {
    &::after {
      content: var(--tw-content);
      rotate: 45deg;
    }
  }
  .after\:rounded-br-sm {
    &::after {
      content: var(--tw-content);
      border-bottom-right-radius: var(--radius-sm);
    }
  }
  .after\:bg-inherit {
    &::after {
      content: var(--tw-content);
      background-color: inherit;
    }
  }
  .last-of-type\:border-r-0 {
    &:last-of-type {
      border-right-style: var(--tw-border-style);
      border-right-width: 0px;
    }
  }
  .hover\:border-\[\#0BB489\] {
    &:hover {
      @media (hover: hover) {
        border-color: #0BB489;
      }
    }
  }
  .hover\:border-\[\#1B44C8\] {
    &:hover {
      @media (hover: hover) {
        border-color: #1B44C8;
      }
    }
  }
  .hover\:border-blue-dark {
    &:hover {
      @media (hover: hover) {
        border-color: #1C3FB7;
      }
    }
  }
  .hover\:border-body-color {
    &:hover {
      @media (hover: hover) {
        border-color: #637381;
      }
    }
  }
  .hover\:border-dark {
    &:hover {
      @media (hover: hover) {
        border-color: #111928;
      }
    }
  }
  .hover\:border-primary {
    &:hover {
      @media (hover: hover) {
        border-color: #3758F9;
      }
    }
  }
  .hover\:border-red-600 {
    &:hover {
      @media (hover: hover) {
        border-color: oklch(0.577 0.245 27.325);
      }
    }
  }
  .hover\:border-stroke {
    &:hover {
      @media (hover: hover) {
        border-color: #DFE4EA;
      }
    }
  }
  .hover\:border-transparent {
    &:hover {
      @media (hover: hover) {
        border-color: transparent;
      }
    }
  }
  .hover\:bg-\[\#0BB489\] {
    &:hover {
      @media (hover: hover) {
        background-color: #0BB489;
      }
    }
  }
  .hover\:bg-\[\#1B44C8\] {
    &:hover {
      @media (hover: hover) {
        background-color: #1B44C8;
      }
    }
  }
  .hover\:bg-\[\#1C9CEA\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #1C9CEA 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#4064AC\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #4064AC 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#D64937\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #D64937 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#E8FBF6\] {
    &:hover {
      @media (hover: hover) {
        background-color: #E8FBF6;
      }
    }
  }
  .hover\:bg-\[\#F5F7FD\] {
    &:hover {
      @media (hover: hover) {
        background-color: #F5F7FD;
      }
    }
  }
  .hover\:bg-black {
    &:hover {
      @media (hover: hover) {
        background-color: #212B36;
      }
    }
  }
  .hover\:bg-blue-dark {
    &:hover {
      @media (hover: hover) {
        background-color: #1C3FB7;
      }
    }
  }
  .hover\:bg-blue-light-5 {
    &:hover {
      @media (hover: hover) {
        background-color: #E1E8FF;
      }
    }
  }
  .hover\:bg-body-color {
    &:hover {
      @media (hover: hover) {
        background-color: #637381;
      }
    }
  }
  .hover\:bg-dark {
    &:hover {
      @media (hover: hover) {
        background-color: #111928;
      }
    }
  }
  .hover\:bg-dark\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #111928 90%, transparent);
      }
    }
  }
  .hover\:bg-gray-1 {
    &:hover {
      @media (hover: hover) {
        background-color: #F9FAFB;
      }
    }
  }
  .hover\:bg-gray-2 {
    &:hover {
      @media (hover: hover) {
        background-color: #F3F4F6;
      }
    }
  }
  .hover\:bg-gray-3 {
    &:hover {
      @media (hover: hover) {
        background-color: #E5E7EB;
      }
    }
  }
  .hover\:bg-gray-4 {
    &:hover {
      @media (hover: hover) {
        background-color: #DEE2E6;
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: oklch(0.985 0.002 247.839);
      }
    }
  }
  .hover\:bg-primary {
    &:hover {
      @media (hover: hover) {
        background-color: #3758F9;
      }
    }
  }
  .hover\:bg-primary\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #3758F9 10%, transparent);
      }
    }
  }
  .hover\:bg-primary\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #3758F9 90%, transparent);
      }
    }
  }
  .hover\:bg-red {
    &:hover {
      @media (hover: hover) {
        background-color: #F23030;
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: oklch(0.577 0.245 27.325);
      }
    }
  }
  .hover\:bg-secondary\/5 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #13C296 5%, transparent);
      }
    }
  }
  .hover\:bg-secondary\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #13C296 90%, transparent);
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:text-body-color {
    &:hover {
      @media (hover: hover) {
        color: #637381;
      }
    }
  }
  .hover\:text-dark {
    &:hover {
      @media (hover: hover) {
        color: #111928;
      }
    }
  }
  .hover\:text-green {
    &:hover {
      @media (hover: hover) {
        color: #22AD5C;
      }
    }
  }
  .hover\:text-green-dark {
    &:hover {
      @media (hover: hover) {
        color: #1A8245;
      }
    }
  }
  .hover\:text-primary {
    &:hover {
      @media (hover: hover) {
        color: #3758F9;
      }
    }
  }
  .hover\:text-red {
    &:hover {
      @media (hover: hover) {
        color: #F23030;
      }
    }
  }
  .hover\:text-secondary {
    &:hover {
      @media (hover: hover) {
        color: #13C296;
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:text-white\/80 {
    &:hover {
      @media (hover: hover) {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:shadow-3 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0px 4px 12px 0px var(--tw-shadow-color, rgba(13, 10, 44, 0.06));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:drop-shadow-testimonial {
    &:hover {
      @media (hover: hover) {
        --tw-drop-shadow: drop-shadow(0px 25px 40px rgba(208, 231, 243, 0.70));
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .file\:hover\:bg-dark\/90 {
    &::file-selector-button {
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in oklab, #111928 90%, transparent);
        }
      }
    }
  }
  .focus\:border-primary {
    &:focus {
      border-color: #3758F9;
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:shadow-none {
    &:focus-visible {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .active\:border-\[\#1B44C8\] {
    &:active {
      border-color: #1B44C8;
    }
  }
  .active\:border-blue-light-5 {
    &:active {
      border-color: #E1E8FF;
    }
  }
  .active\:border-primary {
    &:active {
      border-color: #3758F9;
    }
  }
  .active\:bg-\[\#1B44C8\] {
    &:active {
      background-color: #1B44C8;
    }
  }
  .active\:bg-blue-light-3 {
    &:active {
      background-color: #ADBCF2;
    }
  }
  .active\:bg-blue-light-5 {
    &:active {
      background-color: #E1E8FF;
    }
  }
  .disabled\:border-gray-3 {
    &:disabled {
      border-color: #E5E7EB;
    }
  }
  .disabled\:bg-gray-3 {
    &:disabled {
      background-color: #E5E7EB;
    }
  }
  .disabled\:text-dark-5 {
    &:disabled {
      color: #6B7280;
    }
  }
  .max-sm\:hidden {
    @media (width < 540px) {
      display: none;
    }
  }
  .xs\:block {
    @media (width >= 400px) {
      display: block;
    }
  }
  .xs\:max-w-\[368px\] {
    @media (width >= 400px) {
      max-width: 368px;
    }
  }
  .xs\:min-w-\[368px\] {
    @media (width >= 400px) {
      min-width: 368px;
    }
  }
  .sm\:bottom-0 {
    @media (width >= 540px) {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:container {
    @media (width >= 540px) {
      width: 100%;
      @media (width >= 400px) {
        max-width: 400px;
      }
      @media (width >= 540px) {
        max-width: 540px;
      }
      @media (width >= 720px) {
        max-width: 720px;
      }
      @media (width >= 960px) {
        max-width: 960px;
      }
      @media (width >= 1140px) {
        max-width: 1140px;
      }
      @media (width >= 1320px) {
        max-width: 1320px;
      }
    }
  }
  .sm\:container {
    @media (width >= 540px) {
      margin-inline: auto;
      padding-inline: 16px;
    }
  }
  .sm\:-mx-4 {
    @media (width >= 540px) {
      margin-inline: calc(var(--spacing) * -4);
    }
  }
  .sm\:mr-4 {
    @media (width >= 540px) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .sm\:mb-0 {
    @media (width >= 540px) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:mb-8 {
    @media (width >= 540px) {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  .sm\:block {
    @media (width >= 540px) {
      display: block;
    }
  }
  .sm\:flex {
    @media (width >= 540px) {
      display: flex;
    }
  }
  .sm\:h-\[46px\] {
    @media (width >= 540px) {
      height: 46px;
    }
  }
  .sm\:h-\[60px\] {
    @media (width >= 540px) {
      height: 60px;
    }
  }
  .sm\:h-\[70px\] {
    @media (width >= 540px) {
      height: 70px;
    }
  }
  .sm\:w-1\/2 {
    @media (width >= 540px) {
      width: calc(1/2 * 100%);
    }
  }
  .sm\:w-2\/3 {
    @media (width >= 540px) {
      width: calc(2/3 * 100%);
    }
  }
  .sm\:w-\[46px\] {
    @media (width >= 540px) {
      width: 46px;
    }
  }
  .sm\:w-\[47px\] {
    @media (width >= 540px) {
      width: 47px;
    }
  }
  .sm\:max-w-\[60px\] {
    @media (width >= 540px) {
      max-width: 60px;
    }
  }
  .sm\:max-w-\[70px\] {
    @media (width >= 540px) {
      max-width: 70px;
    }
  }
  .sm\:max-w-\[508px\] {
    @media (width >= 540px) {
      max-width: 508px;
    }
  }
  .sm\:min-w-\[508px\] {
    @media (width >= 540px) {
      min-width: 508px;
    }
  }
  .sm\:flex-row {
    @media (width >= 540px) {
      flex-direction: row;
    }
  }
  .sm\:gap-4 {
    @media (width >= 540px) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .sm\:space-x-4 {
    @media (width >= 540px) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-5 {
    @media (width >= 540px) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:p-6 {
    @media (width >= 540px) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .sm\:p-8 {
    @media (width >= 540px) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .sm\:p-9 {
    @media (width >= 540px) {
      padding: calc(var(--spacing) * 9);
    }
  }
  .sm\:p-10 {
    @media (width >= 540px) {
      padding: calc(var(--spacing) * 10);
    }
  }
  .sm\:p-12 {
    @media (width >= 540px) {
      padding: calc(var(--spacing) * 12);
    }
  }
  .sm\:p-14 {
    @media (width >= 540px) {
      padding: calc(var(--spacing) * 14);
    }
  }
  .sm\:p-\[30px\] {
    @media (width >= 540px) {
      padding: 30px;
    }
  }
  .sm\:px-4 {
    @media (width >= 540px) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:px-6 {
    @media (width >= 540px) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:px-12 {
    @media (width >= 540px) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .sm\:px-\[30px\] {
    @media (width >= 540px) {
      padding-inline: 30px;
    }
  }
  .sm\:py-4 {
    @media (width >= 540px) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .sm\:py-\[30px\] {
    @media (width >= 540px) {
      padding-block: 30px;
    }
  }
  .sm\:pr-10 {
    @media (width >= 540px) {
      padding-right: calc(var(--spacing) * 10);
    }
  }
  .sm\:text-left {
    @media (width >= 540px) {
      text-align: left;
    }
  }
  .sm\:text-2xl {
    @media (width >= 540px) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .sm\:text-4xl {
    @media (width >= 540px) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .sm\:text-lg {
    @media (width >= 540px) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .sm\:text-\[40px\]\/\[48px\] {
    @media (width >= 540px) {
      font-size: 40px;
      line-height: 48px;
    }
  }
  .sm\:text-\[22px\] {
    @media (width >= 540px) {
      font-size: 22px;
    }
  }
  .sm\:text-\[28px\] {
    @media (width >= 540px) {
      font-size: 28px;
    }
  }
  .sm\:text-\[40px\] {
    @media (width >= 540px) {
      font-size: 40px;
    }
  }
  .sm\:text-\[42px\] {
    @media (width >= 540px) {
      font-size: 42px;
    }
  }
  .sm\:text-\[80px\] {
    @media (width >= 540px) {
      font-size: 80px;
    }
  }
  .md\:mr-12 {
    @media (width >= 720px) {
      margin-right: calc(var(--spacing) * 12);
    }
  }
  .md\:mb-0 {
    @media (width >= 720px) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:mb-16 {
    @media (width >= 720px) {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }
  .md\:flex {
    @media (width >= 720px) {
      display: flex;
    }
  }
  .md\:inline-block {
    @media (width >= 720px) {
      display: inline-block;
    }
  }
  .md\:h-\[100px\] {
    @media (width >= 720px) {
      height: 100px;
    }
  }
  .md\:h-\[450px\] {
    @media (width >= 720px) {
      height: 450px;
    }
  }
  .md\:w-1\/2 {
    @media (width >= 720px) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-11\/12 {
    @media (width >= 720px) {
      width: calc(11/12 * 100%);
    }
  }
  .md\:w-\[100px\] {
    @media (width >= 720px) {
      width: 100px;
    }
  }
  .md\:max-w-\[250px\] {
    @media (width >= 720px) {
      max-width: 250px;
    }
  }
  .md\:max-w-\[315px\] {
    @media (width >= 720px) {
      max-width: 315px;
    }
  }
  .md\:max-w-\[630px\] {
    @media (width >= 720px) {
      max-width: 630px;
    }
  }
  .md\:min-w-\[630px\] {
    @media (width >= 720px) {
      min-width: 630px;
    }
  }
  .md\:columns-2 {
    @media (width >= 720px) {
      columns: 2;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 720px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 720px) {
      flex-direction: row;
    }
  }
  .md\:flex-nowrap {
    @media (width >= 720px) {
      flex-wrap: nowrap;
    }
  }
  .md\:gap-6 {
    @media (width >= 720px) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:space-x-8 {
    @media (width >= 720px) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:rounded-b-none {
    @media (width >= 720px) {
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .md\:border-b {
    @media (width >= 720px) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .md\:border-b-0 {
    @media (width >= 720px) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .md\:border-transparent {
    @media (width >= 720px) {
      border-color: transparent;
    }
  }
  .md\:p-7 {
    @media (width >= 720px) {
      padding: calc(var(--spacing) * 7);
    }
  }
  .md\:p-9 {
    @media (width >= 720px) {
      padding: calc(var(--spacing) * 9);
    }
  }
  .md\:p-10 {
    @media (width >= 720px) {
      padding: calc(var(--spacing) * 10);
    }
  }
  .md\:p-\[70px\] {
    @media (width >= 720px) {
      padding: 70px;
    }
  }
  .md\:px-7 {
    @media (width >= 720px) {
      padding-inline: calc(var(--spacing) * 7);
    }
  }
  .md\:px-8 {
    @media (width >= 720px) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .md\:px-10 {
    @media (width >= 720px) {
      padding-inline: calc(var(--spacing) * 10);
    }
  }
  .md\:px-\[60px\] {
    @media (width >= 720px) {
      padding-inline: 60px;
    }
  }
  .md\:px-\[70px\] {
    @media (width >= 720px) {
      padding-inline: 70px;
    }
  }
  .md\:py-3 {
    @media (width >= 720px) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .md\:py-5 {
    @media (width >= 720px) {
      padding-block: calc(var(--spacing) * 5);
    }
  }
  .md\:py-\[60px\] {
    @media (width >= 720px) {
      padding-block: 60px;
    }
  }
  .md\:text-base {
    @media (width >= 720px) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 720px) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .md\:text-\[40px\]\/\[48px\] {
    @media (width >= 720px) {
      font-size: 40px;
      line-height: 48px;
    }
  }
  .md\:text-\[40px\] {
    @media (width >= 720px) {
      font-size: 40px;
    }
  }
  .md\:text-\[100px\] {
    @media (width >= 720px) {
      font-size: 100px;
    }
  }
  .lg\:absolute {
    @media (width >= 960px) {
      position: absolute;
    }
  }
  .lg\:static {
    @media (width >= 960px) {
      position: static;
    }
  }
  .lg\:top-full {
    @media (width >= 960px) {
      top: 100%;
    }
  }
  .lg\:left-0 {
    @media (width >= 960px) {
      left: calc(var(--spacing) * 0);
    }
  }
  .lg\:mt-0 {
    @media (width >= 960px) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:mr-3 {
    @media (width >= 960px) {
      margin-right: calc(var(--spacing) * 3);
    }
  }
  .lg\:mr-14 {
    @media (width >= 960px) {
      margin-right: calc(var(--spacing) * 14);
    }
  }
  .lg\:mb-0 {
    @media (width >= 960px) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:mb-20 {
    @media (width >= 960px) {
      margin-bottom: calc(var(--spacing) * 20);
    }
  }
  .lg\:ml-10 {
    @media (width >= 960px) {
      margin-left: calc(var(--spacing) * 10);
    }
  }
  .lg\:ml-12 {
    @media (width >= 960px) {
      margin-left: calc(var(--spacing) * 12);
    }
  }
  .lg\:ml-auto {
    @media (width >= 960px) {
      margin-left: auto;
    }
  }
  .lg\:block {
    @media (width >= 960px) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 960px) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 960px) {
      display: none;
    }
  }
  .lg\:inline-flex {
    @media (width >= 960px) {
      display: inline-flex;
    }
  }
  .lg\:w-1\/2 {
    @media (width >= 960px) {
      width: calc(1/2 * 100%);
    }
  }
  .lg\:w-1\/3 {
    @media (width >= 960px) {
      width: calc(1/3 * 100%);
    }
  }
  .lg\:w-1\/4 {
    @media (width >= 960px) {
      width: calc(1/4 * 100%);
    }
  }
  .lg\:w-1\/12 {
    @media (width >= 960px) {
      width: calc(1/12 * 100%);
    }
  }
  .lg\:w-2\/12 {
    @media (width >= 960px) {
      width: calc(2/12 * 100%);
    }
  }
  .lg\:w-3\/12 {
    @media (width >= 960px) {
      width: calc(3/12 * 100%);
    }
  }
  .lg\:w-5\/12 {
    @media (width >= 960px) {
      width: calc(5/12 * 100%);
    }
  }
  .lg\:w-6\/12 {
    @media (width >= 960px) {
      width: calc(6/12 * 100%);
    }
  }
  .lg\:w-10\/12 {
    @media (width >= 960px) {
      width: calc(10/12 * 100%);
    }
  }
  .lg\:w-\[630px\] {
    @media (width >= 960px) {
      width: 630px;
    }
  }
  .lg\:w-\[850px\] {
    @media (width >= 960px) {
      width: 850px;
    }
  }
  .lg\:w-auto {
    @media (width >= 960px) {
      width: auto;
    }
  }
  .lg\:w-full {
    @media (width >= 960px) {
      width: 100%;
    }
  }
  .lg\:max-w-\[250px\] {
    @media (width >= 960px) {
      max-width: 250px;
    }
  }
  .lg\:max-w-\[280px\] {
    @media (width >= 960px) {
      max-width: 280px;
    }
  }
  .lg\:max-w-\[738px\] {
    @media (width >= 960px) {
      max-width: 738px;
    }
  }
  .lg\:max-w-full {
    @media (width >= 960px) {
      max-width: 100%;
    }
  }
  .lg\:min-w-\[738px\] {
    @media (width >= 960px) {
      min-width: 738px;
    }
  }
  .lg\:columns-3 {
    @media (width >= 960px) {
      columns: 3;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 960px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 960px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 960px) {
      flex-direction: row;
    }
  }
  .lg\:justify-between {
    @media (width >= 960px) {
      justify-content: space-between;
    }
  }
  .lg\:justify-center {
    @media (width >= 960px) {
      justify-content: center;
    }
  }
  .lg\:justify-end {
    @media (width >= 960px) {
      justify-content: flex-end;
    }
  }
  .lg\:gap-x-5 {
    @media (width >= 960px) {
      column-gap: calc(var(--spacing) * 5);
    }
  }
  .lg\:space-x-14 {
    @media (width >= 960px) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 14) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 14) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .lg\:rounded-xl {
    @media (width >= 960px) {
      border-radius: var(--radius-xl);
    }
  }
  .lg\:bg-transparent {
    @media (width >= 960px) {
      background-color: transparent;
    }
  }
  .lg\:p-8 {
    @media (width >= 960px) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .lg\:px-4 {
    @media (width >= 960px) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .lg\:px-6 {
    @media (width >= 960px) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-7 {
    @media (width >= 960px) {
      padding-inline: calc(var(--spacing) * 7);
    }
  }
  .lg\:px-8 {
    @media (width >= 960px) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:px-10 {
    @media (width >= 960px) {
      padding-inline: calc(var(--spacing) * 10);
    }
  }
  .lg\:px-12 {
    @media (width >= 960px) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .lg\:py-4 {
    @media (width >= 960px) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .lg\:py-5 {
    @media (width >= 960px) {
      padding-block: calc(var(--spacing) * 5);
    }
  }
  .lg\:py-7 {
    @media (width >= 960px) {
      padding-block: calc(var(--spacing) * 7);
    }
  }
  .lg\:py-10 {
    @media (width >= 960px) {
      padding-block: calc(var(--spacing) * 10);
    }
  }
  .lg\:py-\[100px\] {
    @media (width >= 960px) {
      padding-block: 100px;
    }
  }
  .lg\:py-\[120px\] {
    @media (width >= 960px) {
      padding-block: 120px;
    }
  }
  .lg\:pt-0 {
    @media (width >= 960px) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:pt-\[120px\] {
    @media (width >= 960px) {
      padding-top: 120px;
    }
  }
  .lg\:pt-\[150px\] {
    @media (width >= 960px) {
      padding-top: 150px;
    }
  }
  .lg\:pr-0 {
    @media (width >= 960px) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .lg\:pb-20 {
    @media (width >= 960px) {
      padding-bottom: calc(var(--spacing) * 20);
    }
  }
  .lg\:pb-24 {
    @media (width >= 960px) {
      padding-bottom: calc(var(--spacing) * 24);
    }
  }
  .lg\:pb-\[90px\] {
    @media (width >= 960px) {
      padding-bottom: 90px;
    }
  }
  .lg\:pb-\[120px\] {
    @media (width >= 960px) {
      padding-bottom: 120px;
    }
  }
  .lg\:pl-\[120px\] {
    @media (width >= 960px) {
      padding-left: 120px;
    }
  }
  .lg\:text-right {
    @media (width >= 960px) {
      text-align: right;
    }
  }
  .lg\:text-2xl {
    @media (width >= 960px) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .lg\:text-5xl {
    @media (width >= 960px) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:text-base {
    @media (width >= 960px) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .lg\:text-xl {
    @media (width >= 960px) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .lg\:text-\[22px\] {
    @media (width >= 960px) {
      font-size: 22px;
    }
  }
  .lg\:text-\[36px\] {
    @media (width >= 960px) {
      font-size: 36px;
    }
  }
  .lg\:text-\[40px\] {
    @media (width >= 960px) {
      font-size: 40px;
    }
  }
  .lg\:shadow-lg {
    @media (width >= 960px) {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .lg\:shadow-none {
    @media (width >= 960px) {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .xl\:mr-4 {
    @media (width >= 1140px) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .xl\:ml-11 {
    @media (width >= 1140px) {
      margin-left: calc(var(--spacing) * 11);
    }
  }
  .xl\:w-1\/2 {
    @media (width >= 1140px) {
      width: calc(1/2 * 100%);
    }
  }
  .xl\:w-1\/3 {
    @media (width >= 1140px) {
      width: calc(1/3 * 100%);
    }
  }
  .xl\:w-1\/4 {
    @media (width >= 1140px) {
      width: calc(1/4 * 100%);
    }
  }
  .xl\:w-5\/12 {
    @media (width >= 1140px) {
      width: calc(5/12 * 100%);
    }
  }
  .xl\:w-6\/12 {
    @media (width >= 1140px) {
      width: calc(6/12 * 100%);
    }
  }
  .xl\:w-8\/12 {
    @media (width >= 1140px) {
      width: calc(8/12 * 100%);
    }
  }
  .xl\:max-w-\[310px\] {
    @media (width >= 1140px) {
      max-width: 310px;
    }
  }
  .xl\:max-w-\[315px\] {
    @media (width >= 1140px) {
      max-width: 315px;
    }
  }
  .xl\:gap-\[50px\] {
    @media (width >= 1140px) {
      gap: 50px;
    }
  }
  .xl\:p-9 {
    @media (width >= 1140px) {
      padding: calc(var(--spacing) * 9);
    }
  }
  .xl\:p-\[50px\] {
    @media (width >= 1140px) {
      padding: 50px;
    }
  }
  .xl\:px-8 {
    @media (width >= 1140px) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .xl\:px-10 {
    @media (width >= 1140px) {
      padding-inline: calc(var(--spacing) * 10);
    }
  }
  .xl\:px-\[60px\] {
    @media (width >= 1140px) {
      padding-inline: 60px;
    }
  }
  .xl\:pb-0 {
    @media (width >= 1140px) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .xl\:text-2xl {
    @media (width >= 1140px) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .xl\:text-5xl {
    @media (width >= 1140px) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .xl\:text-xl {
    @media (width >= 1140px) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .xl\:text-\[28px\] {
    @media (width >= 1140px) {
      font-size: 28px;
    }
  }
  .xl\:text-\[40px\] {
    @media (width >= 1140px) {
      font-size: 40px;
    }
  }
  .xl\:leading-\[1\.43\] {
    @media (width >= 1140px) {
      --tw-leading: 1.43;
      line-height: 1.43;
    }
  }
  .\32 xl\:bottom-8 {
    @media (width >= 1320px) {
      bottom: calc(var(--spacing) * 8);
    }
  }
  .\32 xl\:mr-16 {
    @media (width >= 1320px) {
      margin-right: calc(var(--spacing) * 16);
    }
  }
  .\32 xl\:w-\[180px\] {
    @media (width >= 1320px) {
      width: 180px;
    }
  }
  .\32 xl\:max-w-\[910px\] {
    @media (width >= 1320px) {
      max-width: 910px;
    }
  }
  .\32 xl\:min-w-\[910px\] {
    @media (width >= 1320px) {
      min-width: 910px;
    }
  }
  .\32 xl\:pl-\[78px\] {
    @media (width >= 1320px) {
      padding-left: 78px;
    }
  }
  .\32 xl\:text-\[22px\] {
    @media (width >= 1320px) {
      font-size: 22px;
    }
  }
  .dark\:block {
    @media (prefers-color-scheme: dark) {
      display: block;
    }
  }
  .dark\:hidden {
    @media (prefers-color-scheme: dark) {
      display: none;
    }
  }
  .dark\:inline-block {
    @media (prefers-color-scheme: dark) {
      display: inline-block;
    }
  }
  .dark\:divide-dark-3 {
    @media (prefers-color-scheme: dark) {
      :where(& > :not(:last-child)) {
        border-color: #374151;
      }
    }
  }
  .dark\:border-dark {
    @media (prefers-color-scheme: dark) {
      border-color: #111928;
    }
  }
  .dark\:border-dark-2 {
    @media (prefers-color-scheme: dark) {
      border-color: #1F2A37;
    }
  }
  .dark\:border-dark-3 {
    @media (prefers-color-scheme: dark) {
      border-color: #374151;
    }
  }
  .dark\:border-dark-4 {
    @media (prefers-color-scheme: dark) {
      border-color: #4B5563;
    }
  }
  .dark\:border-green {
    @media (prefers-color-scheme: dark) {
      border-color: #22AD5C;
    }
  }
  .dark\:border-primary {
    @media (prefers-color-scheme: dark) {
      border-color: #3758F9;
    }
  }
  .dark\:border-transparent {
    @media (prefers-color-scheme: dark) {
      border-color: transparent;
    }
  }
  .dark\:border-white\/5 {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }
  .dark\:border-white\/10 {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .dark\:border-white\/20 {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .dark\:bg-dark {
    @media (prefers-color-scheme: dark) {
      background-color: #111928;
    }
  }
  .dark\:bg-dark-2 {
    @media (prefers-color-scheme: dark) {
      background-color: #1F2A37;
    }
  }
  .dark\:bg-dark-3 {
    @media (prefers-color-scheme: dark) {
      background-color: #374151;
    }
  }
  .dark\:bg-dark-4 {
    @media (prefers-color-scheme: dark) {
      background-color: #4B5563;
    }
  }
  .dark\:bg-dark-5 {
    @media (prefers-color-scheme: dark) {
      background-color: #6B7280;
    }
  }
  .dark\:bg-gray-2 {
    @media (prefers-color-scheme: dark) {
      background-color: #F3F4F6;
    }
  }
  .dark\:bg-gray\/10 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in oklab, #F9FAFB 10%, transparent);
    }
  }
  .dark\:bg-primary\/5 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in oklab, #3758F9 5%, transparent);
    }
  }
  .dark\:bg-primary\/10 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in oklab, #3758F9 10%, transparent);
    }
  }
  .dark\:bg-red-dark {
    @media (prefers-color-scheme: dark) {
      background-color: #E10E0E;
    }
  }
  .dark\:bg-white {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-white);
    }
  }
  .dark\:bg-white\/5 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }
  .dark\:bg-white\/10 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .dark\:from-dark-4 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-from: #4B5563;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-dark-5 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-to: #6B7280;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:text-dark-4 {
    @media (prefers-color-scheme: dark) {
      color: #4B5563;
    }
  }
  .dark\:text-dark-6 {
    @media (prefers-color-scheme: dark) {
      color: #9CA3AF;
    }
  }
  .dark\:text-dark-7 {
    @media (prefers-color-scheme: dark) {
      color: #D1D5DB;
    }
  }
  .dark\:text-gray {
    @media (prefers-color-scheme: dark) {
      color: #F9FAFB;
    }
  }
  .dark\:text-gray-1 {
    @media (prefers-color-scheme: dark) {
      color: #F9FAFB;
    }
  }
  .dark\:text-gray-2 {
    @media (prefers-color-scheme: dark) {
      color: #F3F4F6;
    }
  }
  .dark\:text-light {
    @media (prefers-color-scheme: dark) {
      color: #efefef;
    }
  }
  .dark\:text-primary {
    @media (prefers-color-scheme: dark) {
      color: #3758F9;
    }
  }
  .dark\:text-red {
    @media (prefers-color-scheme: dark) {
      color: #F23030;
    }
  }
  .dark\:text-white {
    @media (prefers-color-scheme: dark) {
      color: var(--color-white);
    }
  }
  .dark\:placeholder-dark-5 {
    @media (prefers-color-scheme: dark) {
      &::placeholder {
        color: #6B7280;
      }
    }
  }
  .dark\:opacity-20 {
    @media (prefers-color-scheme: dark) {
      opacity: 20%;
    }
  }
  .dark\:opacity-30 {
    @media (prefers-color-scheme: dark) {
      opacity: 30%;
    }
  }
  .dark\:shadow-\[0px_20px_95px_0px_rgba\(0\,0\,0\,0\.30\)\] {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0px 20px 95px 0px var(--tw-shadow-color, rgba(0,0,0,0.30));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:shadow-box-dark {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0px 10px 15px 0px var(--tw-shadow-color, rgba(5, 13, 29, 0.18));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:shadow-card {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0px 1px 3px var(--tw-shadow-color, rgba(0, 0, 0, 0.12));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:shadow-none {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:group-hover\:text-primary {
    @media (prefers-color-scheme: dark) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          color: #3758F9;
        }
      }
    }
  }
  .dark\:peer-checked\:bg-dark-3 {
    @media (prefers-color-scheme: dark) {
      &:is(:where(.peer):checked ~ *) {
        background-color: #374151;
      }
    }
  }
  .dark\:peer-checked\:bg-white {
    @media (prefers-color-scheme: dark) {
      &:is(:where(.peer):checked ~ *) {
        background-color: var(--color-white);
      }
    }
  }
  .dark\:file\:border-dark-3 {
    @media (prefers-color-scheme: dark) {
      &::file-selector-button {
        border-color: #374151;
      }
    }
  }
  .dark\:file\:bg-dark-3 {
    @media (prefers-color-scheme: dark) {
      &::file-selector-button {
        background-color: #374151;
      }
    }
  }
  .dark\:file\:bg-white\/5 {
    @media (prefers-color-scheme: dark) {
      &::file-selector-button {
        background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }
  .dark\:file\:text-white {
    @media (prefers-color-scheme: dark) {
      &::file-selector-button {
        color: var(--color-white);
      }
    }
  }
  .dark\:hover\:border-dark {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          border-color: #111928;
        }
      }
    }
  }
  .dark\:hover\:border-dark-3 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          border-color: #374151;
        }
      }
    }
  }
  .dark\:hover\:border-primary {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          border-color: #3758F9;
        }
      }
    }
  }
  .dark\:hover\:bg-dark {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: #111928;
        }
      }
    }
  }
  .dark\:hover\:bg-dark-3 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: #374151;
        }
      }
    }
  }
  .dark\:hover\:bg-primary\/5 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in oklab, #3758F9 5%, transparent);
        }
      }
    }
  }
  .dark\:hover\:bg-white\/5 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
        }
      }
    }
  }
  .dark\:hover\:bg-white\/10 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }
  .dark\:hover\:text-dark-6 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: #9CA3AF;
        }
      }
    }
  }
  .dark\:hover\:text-primary {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: #3758F9;
        }
      }
    }
  }
  .dark\:hover\:text-white {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
  .dark\:hover\:shadow-3 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          --tw-shadow: 0px 4px 12px 0px var(--tw-shadow-color, rgba(13, 10, 44, 0.06));
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
      }
    }
  }
  .dark\:hover\:drop-shadow-none {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          --tw-drop-shadow:  ;
          filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
        }
      }
    }
  }
  .dark\:file\:hover\:bg-white\/10 {
    @media (prefers-color-scheme: dark) {
      &::file-selector-button {
        &:hover {
          @media (hover: hover) {
            background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
          }
        }
      }
    }
  }
  .dark\:focus\:border-primary {
    @media (prefers-color-scheme: dark) {
      &:focus {
        border-color: #3758F9;
      }
    }
  }
  .lg\:dark\:bg-transparent {
    @media (width >= 960px) {
      @media (prefers-color-scheme: dark) {
        background-color: transparent;
      }
    }
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}
.snap {
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
}
.snap::-webkit-scrollbar {
  display: none;
}
.snap > img {
  scroll-snap-align: center;
}
.navbarTogglerActive > span:nth-child(1) {
  top: 7px;
  rotate: 45deg;
}
.navbarTogglerActive > span:nth-child(2) {
  opacity: 0%;
}
.navbarTogglerActive > span:nth-child(3) {
  top: calc(var(--spacing) * -2);
  rotate: 135deg;
}
input:checked ~ .dot {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
  background-color: #3758F9;
}
input:checked ~ .dot .active {
  display: block;
}
input:checked ~ .dot .inactive {
  display: none;
}
input#toggleFour:checked ~ .box {
  background-color: #3758F9;
}
input#toggleFour:checked ~ .dot {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
  background-color: var(--color-white);
}
input#toggleFive:checked ~ .dot {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
  background-color: var(--color-white);
}
input#toggleFive:checked ~ .dot > span {
  background-color: #3758F9;
}
input#toggleSix:checked ~ .dot {
  --tw-translate-x: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
  background-color: var(--color-white);
}
input#toggleEight:checked ~ .box {
  background-color: #EAEEFB;
}
input#toggleEight:checked ~ .dot {
  background-color: #3758F9;
}
input#toggleEight:checked ~ .dot span {
  border-color: var(--color-white);
  background-color: #3758F9;
}
input#toggleNine:checked ~ .dot span {
  background-color: var(--color-white);
}
input#toggleNine:checked ~ .dot {
  background-color: #3758F9;
}
input:checked ~ .box {
  border-color: #3758F9;
}
input#checkboxLabelOne:checked ~ .box {
  border-color: #3758F9;
}
input#checkboxLabelOne:checked ~ .box .dot {
  background-color: #3758F9;
}
input#checkboxLabelTwo:checked ~ .box span {
  opacity: 100%;
}
input#checkboxLabelThree:checked ~ .box span {
  opacity: 100%;
}
input#checkboxLabelFour:checked ~ .box {
  border-color: #3758F9;
}
input#checkboxLabelFour:checked ~ .box span {
  background-color: #3758F9;
}
input#checkboxLabelFive:checked ~ .box {
  background-color: #3758F9;
}
.shape-gradient {
  background: linear-gradient( 180deg, rgba(255, 255, 255, 0.08) 0%, rgba(196, 196, 196, 0) 100% );
}
.container {
  margin-inline: auto;
  padding-inline: calc(var(--spacing) * 4);
}
input[type="checkbox"]:checked ~ .box span {
  opacity: 100%;
}
input[type="radio"]:checked ~ .box .circle {
  background-color: #3758F9;
}
input[type="radio"]:checked ~ .box span {
  opacity: 100%;
}
.payment:checked ~ label {
  border-color: #3758F9;
  background-color: color-mix(in oklab, #3758F9 8%, transparent);
}
.shipping:checked ~ label {
  border-color: #3758F9;
}
.shipping:checked ~ label .title {
  color: #3758F9;
}
.color:checked ~ label span {
  height: calc(var(--spacing) * 2);
  width: calc(var(--spacing) * 2);
}
.productColor:checked ~ label span {
  height: calc(var(--spacing) * 7);
  width: calc(var(--spacing) * 7);
}
.productColor2:checked ~ label span {
  height: calc(var(--spacing) * 3);
  width: calc(var(--spacing) * 3);
}
.filter-size:checked ~ label {
  border-color: #3758F9;
  background-color: #3758F9;
  color: var(--color-white);
}
.filter-size-2:checked ~ label {
  border-color: #3758F9;
  background-color: color-mix(in oklab, #3758F9 10%, transparent);
}
.ram-size:checked ~ label {
  border-color: #3758F9;
  color: #3758F9;
}
.download-radio:checked ~ label {
  border-color: #3758F9;
  background-color: #3758F9;
}
.download-radio:checked ~ label span {
  color: var(--color-white);
}
.download-radio:checked ~ label .icon {
  opacity: 100%;
}
.priceSlideOne .noUi-target {
  margin-top: calc(var(--spacing) * 8);
  --tw-border-style: none;
  border-style: none;
  background-color: #f4f7ff;
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.priceSlideOne .noUi-connects {
  height: 6px;
  border-radius: calc(infinity * 1px);
  background-color: #D4D9E8;
}
.priceSlideOne .noUi-connect {
  height: 6px;
  border-radius: calc(infinity * 1px);
  background-color: #3758F9;
}
.priceSlideOne .noUi-horizontal .noUi-handle {
  top: calc(var(--spacing) * -2);
  height: 22px;
  width: 22px;
  border-radius: calc(infinity * 1px);
  border-style: var(--tw-border-style);
  border-width: 6px;
  border-color: #3758F9;
  background-color: var(--color-white);
}
.priceSlideTwo .noUi-target {
  margin-top: calc(var(--spacing) * 8);
  --tw-border-style: none;
  border-style: none;
  background-color: var(--color-white);
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.priceSlideTwo .noUi-connects {
  height: calc(var(--spacing) * 1);
  border-radius: calc(infinity * 1px);
  background-color: #D4D9E8;
}
.priceSlideTwo .noUi-connect {
  height: calc(var(--spacing) * 1);
  border-radius: calc(infinity * 1px);
  background-color: #3758F9;
}
.priceSlideTwo .noUi-horizontal .noUi-handle {
  top: calc(var(--spacing) * -3);
  height: 30px;
  width: 30px;
  border-radius: calc(infinity * 1px);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: #3758F9;
  background-color: var(--color-white);
}
.noUi-horizontal .noUi-handle:after, .noUi-horizontal .noUi-handle:before {
  display: none;
}
#activityChart .apexcharts-legend-series {
  margin-right: calc(var(--spacing) * 5) !important;
}
.autoSaverSwitch input:checked ~ .slider {
  background-color: #3758F9;
}
.autoSaverSwitch input:checked ~ .slider .dot {
  --tw-translate-x: calc(var(--spacing) * 6);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.autoSaverSwitch input:checked ~ .label .on {
  display: block;
}
.autoSaverSwitch input:checked ~ .label .off {
  display: none;
}
.themeSwitcherTwo input:checked ~ .light {
  background-color: transparent;
  color: #637381;
}
.themeSwitcherTwo input:checked ~ .dark {
  background-color: #F9FAFB;
  color: #3758F9;
}
.themeSwitcherTwo input:checked ~ .slider {
  background-color: #212B36;
}
.themeSwitcherTwo input:checked ~ .slider .dot {
  --tw-translate-x: 28px;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.themeSwitcherThree input:checked ~ div .light {
  background-color: var(--color-white);
  color: #637381;
}
.themeSwitcherThree input:checked ~ div .dark {
  background-color: #3758F9;
  color: var(--color-white);
}
.checkbox-list:checked ~ label {
  border-color: #3758F9;
  background-color: #3758F9;
}
.checkbox-list:checked ~ label .icon {
  opacity: 100%;
}
.box-select-1:checked ~ label .box {
  border-color: #3758F9;
  background-color: #3758F9;
}
.box-select-1:checked ~ label .box .icon {
  opacity: 100%;
}
.box-select-1:checked ~ label div.user-box {
  background-color: #F3F4F6;
}
.select-list:checked ~ label {
  border-color: #3758F9;
  color: #3758F9;
}
.select-list:checked ~ label .icon {
  background-color: #3758F9;
}
.tableCheckbox:checked ~ label .icon-box {
  border-color: #3758F9;
  background-color: #3758F9;
}
.tableCheckbox:checked ~ label .icon {
  opacity: 100%;
}
.tableCheckbox-2:checked ~ label {
  border-color: #3758F9;
  background-color: #3758F9;
}
.tableCheckbox-2:checked ~ label .icon {
  color: var(--color-white);
  opacity: 100%;
}
.jvm-zoom-btn {
  top: auto;
  bottom: calc(var(--spacing) * 0);
  left: auto;
  display: flex;
  height: calc(var(--spacing) * 8);
  width: calc(var(--spacing) * 8);
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: .5px;
  border-color: #DFE4EA;
  background-color: #F9FAFB;
  --tw-leading: 1;
  line-height: 1;
  --tw-font-weight: var(--font-weight-semibold);
  font-weight: var(--font-weight-semibold);
  color: #637381;
  &:hover {
    @media (hover: hover) {
      border-color: #3758F9;
    }
  }
  &:hover {
    @media (hover: hover) {
      background-color: #3758F9;
    }
  }
  &:hover {
    @media (hover: hover) {
      color: var(--color-white);
    }
  }
}
.mapOne .jvm-zoom-btn {
  top: auto;
  bottom: calc(var(--spacing) * 0);
  left: auto;
}
.mapOne .jvm-zoom-btn.jvm-zoomin {
  right: calc(var(--spacing) * 9);
}
.mapOne .jvm-zoom-btn.jvm-zoomout {
  right: calc(var(--spacing) * 0);
}
.mapTwo .jvm-zoom-btn {
  top: auto;
  bottom: calc(var(--spacing) * 0);
}
.mapTwo .jvm-zoom-btn.jvm-zoomin {
  left: calc(var(--spacing) * 0);
}
.mapTwo .jvm-zoom-btn.jvm-zoomout {
  left: calc(var(--spacing) * 9);
}
.mapFour .jvm-zoom-btn {
  top: calc(var(--spacing) * -20);
}
.mapFour .jvm-zoom-btn.jvm-zoomin {
  right: calc(var(--spacing) * 9);
}
.mapFour .jvm-zoom-btn.jvm-zoomout {
  right: calc(var(--spacing) * 0);
}
@layer base {
  .snap {
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
  }
  .snap::-webkit-scrollbar {
    display: none;
  }
  .snap > img {
    scroll-snap-align: center;
  }
  .navbarTogglerActive > span:nth-child(1) {
    transform: rotate(45deg);
    top: 7px;
  }
  .navbarTogglerActive > span:nth-child(2) {
    opacity: 0;
  }
  .navbarTogglerActive > span:nth-child(3) {
    top: -8px;
    transform: rotate(135deg);
  }
  .shape-gradient {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.08) 0%, rgba(196, 196, 196, 0) 100%);
  }
  .container {
    margin-left: auto;
    margin-r-ight: auto;
    padding-left: 16px;
    padding-r-ight: 16px;
  }
  input[type="checkbox"]:checked ~ .box span {
    opacity: 1;
  }
  input[type="radio"]:checked ~ .box .circle {
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  input[type="radio"]:checked ~ .box span {
    opacity: 1;
  }
  .payment:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background-opacity: 8%;
  }
  .shipping:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .shipping:checked ~ label .title {
    color {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .color:checked ~ label span {
    width: 8px;
    height: 8px;
  }
  .productColor:checked ~ label span {
    height: 28px;
    width: 28px;
  }
  .productColor2:checked ~ label span {
    height: 12px;
    width: 12px;
  }
  .filter-size:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
    color: #fff;
  }
  .filter-size-2:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background-opacity: 10%;
  }
  .ram-size:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    color {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .download-radio:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .download-radio:checked ~ label span {
    color: #fff;
  }
  .download-radio:checked ~ label .icon {
    opacity: 1;
  }
  .noUi-base {
    background: #fff;
  }
  .dark-mode .noUi-base {
    background: undefined;
  }
  .apexcharts-text {
    fill: #637381;
  }
  .dark-mode .apexcharts-text {
    fill: undefined;
  }
  .apexcharts-legend-text {
    color: #637381;
  }
  .dark-mode .apexcharts-legend-text {
    color: undefined;
  }
  .apexcharts-xcrosshairs {
    fill {
      -d-e-f-a-u-l-t: #DFE4EA;
    }
  }
  .dark-mode .apexcharts-xcrosshairs {
    fill: undefined;
  }
  .apexcharts-gridline {
    fill {
      -d-e-f-a-u-l-t: #DFE4EA;
    }
  }
  .dark-mode .apexcharts-gridline {
    fill: undefined;
  }
  .apexcharts-legend-marker {
    top: 0;
  }
  .chart-10 .apexcharts-tooltip-text {
    color: #fff;
  }
  .priceSlideOne .noUi-target {
    margin-top: 32px;
    border: none;
    background: #f4f7ff;
    box-shadow: none;
  }
  .priceSlideOne .noUi-connects {
    height: 6px;
    border-radius: 99999;
    background: #d4d9e8;
  }
  .dark-mode .priceSlideOne .noUi-connects {
    background: undefined;
  }
  .priceSlideOne .noUi-connect {
    height: 6px;
    border-radius: 999px;
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .priceSlideOne .noUi-horizontal .noUi-handle {
    top: -8px;
    height: 22px;
    width: 22px;
    border: 6px solid;
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background: #fff;
    box-shadow: none;
  }
  .dark-mode .priceSlideOne .noUi-horizontal .noUi-handle {
    background: undefined;
  }
  .priceSlideTwo .noUi-target {
    margin-top: 32px;
    border: none;
    background: #fff;
    box-shadow: none;
  }
  .priceSlideTwo .noUi-connects {
    height: 4px;
    border-radius: 99999;
    background: #d4d9e8;
  }
  .priceSlideTwo .noUi-connect {
    height: 4px;
    border-radius: 99999;
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .priceSlideTwo .noUi-horizontal .noUi-handle {
    top: -12px;
    height: 30px;
    width: 30px;
    border-radius: 999px;
    border: 1px solid;
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background: #fff;
  }
  .noUi-horizontal .noUi-handle:after {
    display: none;
  }
  .noUi-horizontal .noUi-handle:before {
    display: none;
  }
  #activityChart .apexcharts-legend-series {
    margin-right: 20px !important;
  }
  .checkbox-list:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .checkbox-list:checked ~ label .icon {
    opacity: 1;
  }
  .box-select-1:checked ~ label .box {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .box-select-1:checked ~ label .box .icon {
    opacity: 1;
  }
  .box-select-1:checked ~ label div.user-box {
    background: #F8FAFC;
  }
  .select-list:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    color {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .select-list:checked ~ label .icon {
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .tableCheckbox:checked ~ label .icon-box {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .tableCheckbox:checked ~ label .icon {
    opacity: 1;
  }
  .tableCheckbox-2:checked ~ label {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
  }
  .tableCheckbox-2:checked ~ label .icon {
    color: #fff;
    opacity: 1;
  }
  .jvm-zoom-btn {
    top: auto;
    bottom: 0;
    left: auto;
    display: flex;
    height: 32px;
    width: 32px;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: .5px solid #E7E7E7;
    background: #f4f7ff;
    font-weight: semibold;
    line-height: none;
    color: #637381;
  }
  .jvm-zoom-btn:hover {
    borderColor {
      -d-e-f-a-u-l-t: #3758F9;
    }
    background {
      -d-e-f-a-u-l-t: #3758F9;
    }
    color: #fff;
  }
  .mapOne .jvm-zoom-btn {
    top: auto;
    bottom: 0;
    left: auto;
  }
  .mapOne .jvm-zoom-btn.jvm-zoomin {
    right: 36px;
  }
  .mapOne .jvm-zoom-btn.jvm-zoomout {
    right: 0;
  }
  .mapTwo .jvm-zoom-btn {
    top: auto;
    bottom: 0;
  }
  .mapTwo .jvm-zoom-btn.jvm-zoomin {
    left: 0;
  }
  .mapTwo .jvm-zoom-btn.jvm-zoomout {
    left: 36px;
  }
  .mapFour .jvm-zoom-btn {
    top: -80px;
  }
  .mapFour .jvm-zoom-btn.jvm-zoomin {
    right: 36px;
  }
  .mapFour .jvm-zoom-btn.jvm-zoomout {
    right: 0;
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
